package io.github.chenygs.im;

import io.github.chenygs.im.config.IMAutoConfiguration;
import io.github.chenygs.im.core.id.MessageIdGenerator;
import io.github.chenygs.im.core.session.SessionManager;
import io.github.chenygs.im.service.MessageSendService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IM应用测试
 * 
 * <AUTHOR>
 */
@SpringBootTest(classes = IMAutoConfiguration.class)
@TestPropertySource(properties = {
    "im.enabled=true",
    "im.server.port=8081"
})
public class IMApplicationTest {
    
    @Autowired
    private MessageIdGenerator messageIdGenerator;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageSendService messageSendService;
    
    @Test
    public void testAutoConfiguration() {
        assertNotNull(messageIdGenerator);
        assertNotNull(sessionManager);
        assertNotNull(messageSendService);
    }
    
    @Test
    public void testMessageIdGenerator() {
        String id1 = messageIdGenerator.nextId();
        String id2 = messageIdGenerator.nextId();
        
        assertNotNull(id1);
        assertNotNull(id2);
        assertNotEquals(id1, id2);
        
        // 测试趋势递增
        long longId1 = Long.parseLong(id1);
        long longId2 = Long.parseLong(id2);
        assertTrue(longId2 > longId1);
    }
    
    @Test
    public void testSessionManager() {
        // 测试在线用户数量
        long count = sessionManager.getOnlineUserCount();
        assertTrue(count >= 0);
        
        // 测试在线用户列表
        var onlineUsers = sessionManager.getOnlineUserIds();
        assertNotNull(onlineUsers);
    }
}
