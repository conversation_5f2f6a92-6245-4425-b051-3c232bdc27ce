package io.github.chenygs.im.core.id;

import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认消息ID生成器实现
 * 基于雪花算法的变种，保证全局唯一和趋势递增
 * 
 * <AUTHOR>
 */
@Component
public class DefaultMessageIdGenerator implements MessageIdGenerator {
    
    /**
     * 机器ID位数
     */
    private static final long MACHINE_ID_BITS = 10L;
    
    /**
     * 序列号位数
     */
    private static final long SEQUENCE_BITS = 12L;
    
    /**
     * 机器ID最大值
     */
    private static final long MAX_MACHINE_ID = ~(-1L << MACHINE_ID_BITS);
    
    /**
     * 序列号最大值
     */
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);
    
    /**
     * 机器ID左移位数
     */
    private static final long MACHINE_ID_SHIFT = SEQUENCE_BITS;
    
    /**
     * 时间戳左移位数
     */
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;
    
    /**
     * 起始时间戳 (2024-01-01 00:00:00)
     */
    private static final long START_TIMESTAMP = 1704067200000L;
    
    /**
     * 机器ID
     */
    private final long machineId;
    
    /**
     * 序列号
     */
    private final AtomicLong sequence = new AtomicLong(0L);
    
    /**
     * 上次生成ID的时间戳
     */
    private volatile long lastTimestamp = -1L;
    
    public DefaultMessageIdGenerator() {
        this.machineId = generateMachineId();
    }
    
    public DefaultMessageIdGenerator(long machineId) {
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw new IllegalArgumentException(
                String.format("Machine ID can't be greater than %d or less than 0", MAX_MACHINE_ID));
        }
        this.machineId = machineId;
    }
    
    @Override
    public synchronized String nextId() {
        long timestamp = getCurrentTimestamp();
        
        // 时钟回拨检查
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", 
                    lastTimestamp - timestamp));
        }
        
        long currentSequence;
        if (timestamp == lastTimestamp) {
            // 同一毫秒内，序列号递增
            currentSequence = sequence.incrementAndGet() & MAX_SEQUENCE;
            if (currentSequence == 0) {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp);
                sequence.set(0L);
                currentSequence = 0L;
            }
        } else {
            // 不同毫秒，序列号重置
            sequence.set(0L);
            currentSequence = 0L;
        }
        
        lastTimestamp = timestamp;
        
        // 生成ID
        long id = ((timestamp - START_TIMESTAMP) << TIMESTAMP_SHIFT)
                | (machineId << MACHINE_ID_SHIFT)
                | currentSequence;
        
        return String.valueOf(id);
    }
    
    /**
     * 等待下一毫秒
     */
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }
    
    /**
     * 获取当前时间戳
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    /**
     * 生成机器ID
     */
    private long generateMachineId() {
        try {
            InetAddress ip = InetAddress.getLocalHost();
            byte[] ipBytes = ip.getAddress();
            long machineId = 0L;
            for (byte b : ipBytes) {
                machineId = (machineId << 8) | (b & 0xFF);
            }
            return machineId & MAX_MACHINE_ID;
        } catch (UnknownHostException e) {
            // 如果无法获取IP，使用当前时间的低位作为机器ID
            return System.currentTimeMillis() & MAX_MACHINE_ID;
        }
    }
    
    /**
     * 获取机器ID
     */
    public long getMachineId() {
        return machineId;
    }
}
