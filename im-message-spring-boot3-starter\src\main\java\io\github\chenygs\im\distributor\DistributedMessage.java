package io.github.chenygs.im.distributor;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分布式消息模型
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DistributedMessage {
    
    /**
     * 源节点ID
     */
    private String sourceNodeId;
    
    /**
     * 目标节点ID
     */
    private String targetNodeId;
    
    /**
     * 目标用户ID
     */
    private String targetUserId;
    
    /**
     * 目标设备ID
     */
    private String targetDeviceId;
    
    /**
     * 消息内容
     */
    private Object message;
    
    /**
     * 消息类型
     */
    private MessageType messageType;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 分布式消息类型
     */
    public enum MessageType {
        /**
         * 用户消息
         */
        USER_MESSAGE,
        
        /**
         * 用户设备消息
         */
        USER_DEVICE_MESSAGE,
        
        /**
         * 广播消息
         */
        BROADCAST_MESSAGE,
        
        /**
         * 节点消息
         */
        NODE_MESSAGE,
        
        /**
         * 节点广播消息
         */
        NODE_BROADCAST_MESSAGE
    }
}
