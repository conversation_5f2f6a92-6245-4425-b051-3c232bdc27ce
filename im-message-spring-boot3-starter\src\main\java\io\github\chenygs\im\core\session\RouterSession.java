package io.github.chenygs.im.core.session;

import io.github.chenygs.im.core.client.ClientSender;
import io.github.chenygs.im.core.model.ImS2C;
import io.github.chenygs.im.util.SpringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 路由会话实现
 * 用于处理远程连接的消息发送
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
public class RouterSession extends AbstractServerSession {
    
    /**
     * 会话缓存信息
     */
    private SessionCache sessionCache;
    
    public RouterSession(SessionCache sessionCache) {
        this.sessionCache = sessionCache;
    }
    
    @Override
    public boolean writeAndFlush(ImS2C s2cInfo) {
        try {
            ClientSender clientSender = SpringUtil.getBean(ClientSender.class);
            
            // 设置目标服务器信息
            if (sessionCache != null && sessionCache.getServerNode() != null) {
                String serverId = sessionCache.getServerNode().getServerId();
                log.debug("发送远程消息到服务器: {}, 用户: {}, 消息: {}", 
                    serverId, sessionCache.getUserId(), s2cInfo.getMessageType());
                
                return clientSender.sendMessage(s2cInfo, serverId);
            } else {
                log.debug("发送远程消息, 用户: {}, 消息: {}", 
                    sessionCache != null ? sessionCache.getUserId() : "unknown", s2cInfo.getMessageType());
                
                return clientSender.sendMessage(s2cInfo);
            }
        } catch (Exception e) {
            log.error("远程消息发送失败, 用户: {}, 消息类型: {}", 
                sessionCache != null ? sessionCache.getUserId() : "unknown", 
                s2cInfo.getMessageType(), e);
            return false;
        }
    }
    
    /**
     * 获取用户ID
     * 
     * @return 用户ID
     */
    public Long getUserId() {
        return sessionCache != null ? sessionCache.getUserId() : null;
    }
    
    /**
     * 获取会话ID
     * 
     * @return 会话ID
     */
    public String getSessionId() {
        return sessionCache != null ? sessionCache.getSessionId() : null;
    }
    
    /**
     * 获取设备ID
     * 
     * @return 设备ID
     */
    public String getDeviceId() {
        return sessionCache != null ? sessionCache.getDeviceId() : null;
    }
    
    /**
     * 获取服务器ID
     * 
     * @return 服务器ID
     */
    public String getServerId() {
        return sessionCache != null && sessionCache.getServerNode() != null ? 
            sessionCache.getServerNode().getServerId() : null;
    }
    
    /**
     * 检查会话是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return sessionCache != null && sessionCache.getServerNode() != null;
    }
    
    @Override
    public String toString() {
        return "RouterSession{" +
                "userId=" + getUserId() +
                ", sessionId='" + getSessionId() + '\'' +
                ", deviceId='" + getDeviceId() + '\'' +
                ", serverId='" + getServerId() + '\'' +
                ", valid=" + isValid() +
                '}';
    }
}
