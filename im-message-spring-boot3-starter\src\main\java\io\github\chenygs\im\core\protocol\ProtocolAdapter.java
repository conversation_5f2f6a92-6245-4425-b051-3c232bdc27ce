package io.github.chenygs.im.core.protocol;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPipeline;

/**
 * 协议适配器接口
 * 用于操作不同协议下的消息处理
 * 
 * <AUTHOR>
 */
public interface ProtocolAdapter {
    
    /**
     * 获取协议类型
     * 
     * @return 协议类型
     */
    String getProtocolType();
    
    /**
     * 配置通道管道
     * 
     * @param pipeline Netty管道
     */
    void configurePipeline(ChannelPipeline pipeline);
    
    /**
     * 编码消息
     * 
     * @param message 原始消息
     * @return 编码后的消息
     */
    Object encodeMessage(Object message);
    
    /**
     * 解码消息
     * 
     * @param message 编码的消息
     * @return 解码后的消息
     */
    Object decodeMessage(Object message);
    
    /**
     * 发送消息
     * 
     * @param ctx 通道上下文
     * @param message 消息
     */
    void sendMessage(ChannelHandlerContext ctx, Object message);
    
    /**
     * 关闭连接
     * 
     * @param ctx 通道上下文
     */
    void closeConnection(ChannelHandlerContext ctx);
    
    /**
     * 获取客户端IP地址
     * 
     * @param ctx 通道上下文
     * @return IP地址
     */
    String getClientIp(ChannelHandlerContext ctx);
}
