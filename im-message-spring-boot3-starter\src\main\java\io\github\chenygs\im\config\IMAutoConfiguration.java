package io.github.chenygs.im.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.chenygs.im.core.adapter.ChannelAdapter;
import io.github.chenygs.im.core.adapter.DefaultChannelAdapter;
import io.github.chenygs.im.core.client.ClientSender;
import io.github.chenygs.im.core.client.DefaultClientSender;
import io.github.chenygs.im.core.distributor.MessageDistributor;
import io.github.chenygs.im.core.id.DefaultMessageIdGenerator;
import io.github.chenygs.im.core.id.MessageIdGenerator;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.github.chenygs.im.core.session.*;
import io.github.chenygs.im.distributor.RedissonMessageDistributor;
import io.github.chenygs.im.protocol.websocket.WebSocketProtocolAdapter;
import io.github.chenygs.im.server.NettyIMServer;
import io.github.chenygs.im.service.MessageSendService;
import io.github.chenygs.im.service.SessionService;
import io.github.chenygs.im.session.RedissonSessionManager;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * IM系统自动配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(IMProperties.class)
@ConditionalOnProperty(prefix = "im", name = "enabled", havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = "io.github.chenygs.im")
public class IMAutoConfiguration {
    
    /**
     * 消息ID生成器
     */
    @Bean
    @ConditionalOnMissingBean
    public MessageIdGenerator messageIdGenerator() {
        return new DefaultMessageIdGenerator();
    }
    
    /**
     * 协议适配器
     */
    @Bean
    @ConditionalOnMissingBean
    public ProtocolAdapter protocolAdapter(ObjectMapper objectMapper) {
        WebSocketProtocolAdapter adapter = new WebSocketProtocolAdapter();
        // 手动注入ObjectMapper，因为@Autowired在@Bean方法中可能不工作
        try {
            var field = WebSocketProtocolAdapter.class.getDeclaredField("objectMapper");
            field.setAccessible(true);
            field.set(adapter, objectMapper);
        } catch (Exception e) {
            log.warn("Failed to inject ObjectMapper into WebSocketProtocolAdapter", e);
        }
        return adapter;
    }
    
    /**
     * 本地会话管理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "im.session", name = "type", havingValue = "local", matchIfMissing = true)
    public SessionManager localSessionManager() {
        return new LocalSessionManager();
    }
    
    /**
     * Redis会话管理器
     */
    @Bean
    @ConditionalOnClass(RedissonClient.class)
    @ConditionalOnProperty(prefix = "im.session", name = "type", havingValue = "redis")
    public SessionManager redisSessionManager() {
        return new RedissonSessionManager();
    }
    
    /**
     * 通道适配器
     */
    @Bean
    @ConditionalOnMissingBean
    public ChannelAdapter channelAdapter() {
        return new DefaultChannelAdapter();
    }
    
    /**
     * 消息发送服务
     */
    @Bean
    @ConditionalOnMissingBean
    public MessageSendService messageSendService() {
        return new MessageSendService();
    }
    
    /**
     * 服务端会话管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public ServerSessionManager serverSessionManager() {
        return new DefaultServerSessionManager();
    }

    /**
     * 会话服务
     */
    @Bean
    @ConditionalOnMissingBean
    public SessionService sessionService() {
        return new SessionService();
    }

    /**
     * 客户端消息发送器
     */
    @Bean
    @ConditionalOnMissingBean
    public ClientSender clientSender() {
        return new DefaultClientSender();
    }

    /**
     * Redis消息分发器
     */
    @Bean
    @ConditionalOnClass(RedissonClient.class)
    @ConditionalOnProperty(prefix = "im.distributed", name = "enabled", havingValue = "true")
    @ConditionalOnProperty(prefix = "im.distributed", name = "distributor", havingValue = "redis", matchIfMissing = true)
    public MessageDistributor redisMessageDistributor() {
        return new RedissonMessageDistributor();
    }
    
    /**
     * Netty IM服务器
     */
    @Bean
    @ConditionalOnMissingBean
    public NettyIMServer nettyIMServer(IMProperties properties, 
                                       ProtocolAdapter protocolAdapter,
                                       ChannelAdapter channelAdapter) {
        return new NettyIMServer(properties, protocolAdapter, channelAdapter);
    }
    
    /**
     * ObjectMapper配置
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        return mapper;
    }
}
