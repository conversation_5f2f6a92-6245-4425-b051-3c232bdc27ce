# IM系统配置示例
im:
  enabled: true
  server:
    port: 8080
    host: 0.0.0.0
    boss-threads: 1
    worker-threads: 0
    tcp-nodelay: true
    keep-alive: true
    backlog: 128
  websocket:
    path: /ws
    max-frame-size: 65536
    compression: true
    read-idle-time: 60
    write-idle-time: 0
    all-idle-time: 0
  session:
    type: local  # local, redis
    distributed: false
    cleanup-interval: 300
    timeout: 3600
  message:
    id-generator: default
    persistence: false
    max-size: 1048576
    expire-days: 30
    max-offline-messages: 1000
  distributed:
    enabled: false
    node-id: ${spring.application.name:im-server}-${server.port:8080}
    distributor: redis
    redis:
      user-message-topic-prefix: "im:user:message:"
      node-message-topic-prefix: "im:node:message:"
      broadcast-message-topic: "im:broadcast:message"
      online-users-key: "im:session:online_users"
      user-session-key-prefix: "im:session:user:"
      channel-info-key-prefix: "im:session:channel:"

# Spring Boot配置
spring:
  application:
    name: im-message-demo
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

# 日志配置
logging:
  level:
    io.github.chenygs.im: DEBUG
    io.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
