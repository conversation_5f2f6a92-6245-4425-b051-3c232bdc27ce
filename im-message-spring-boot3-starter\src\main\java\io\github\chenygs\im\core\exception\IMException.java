package io.github.chenygs.im.core.exception;

/**
 * IM系统统一运行时异常
 * 
 * <AUTHOR>
 */
public class IMException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误数据
     */
    private Object errorData;
    
    public IMException(String message) {
        super(message);
    }
    
    public IMException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public IMException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public IMException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public IMException(String errorCode, String message, Object errorData) {
        super(message);
        this.errorCode = errorCode;
        this.errorData = errorData;
    }
    
    public IMException(String errorCode, String message, Object errorData, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorData = errorData;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public Object getErrorData() {
        return errorData;
    }
    
    public void setErrorData(Object errorData) {
        this.errorData = errorData;
    }
    
    /**
     * 常用错误码定义
     */
    public static class ErrorCodes {
        public static final String INVALID_MESSAGE_TYPE = "INVALID_MESSAGE_TYPE";
        public static final String USER_NOT_ONLINE = "USER_NOT_ONLINE";
        public static final String CHANNEL_NOT_FOUND = "CHANNEL_NOT_FOUND";
        public static final String MESSAGE_SEND_FAILED = "MESSAGE_SEND_FAILED";
        public static final String PROTOCOL_NOT_SUPPORTED = "PROTOCOL_NOT_SUPPORTED";
        public static final String SESSION_EXPIRED = "SESSION_EXPIRED";
        public static final String AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED";
        public static final String PERMISSION_DENIED = "PERMISSION_DENIED";
        public static final String MESSAGE_TOO_LARGE = "MESSAGE_TOO_LARGE";
        public static final String RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED";
    }
}
