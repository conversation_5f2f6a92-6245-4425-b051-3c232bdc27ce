# IM Message Spring Boot 3 Starter

基于 Spring Boot 3 + JDK 21 + Netty 4.1.115.Final 实现的即时通讯插件，支持可插拔架构和分布式部署。

## 特性

- 🚀 **可插拔架构**: 支持多协议扩展（WebSocket、TCP、UDP等）
- 📱 **多端支持**: 支持同一用户多设备同时在线
- 🔄 **消息类型**: 支持心跳、私信、群组、系统通知、ACK等多种消息类型
- 🌐 **分布式**: 基于 Redis 的分布式会话管理和消息转发
- 💾 **消息持久化**: 支持消息存储和离线消息推送
- 🔧 **Spring Boot 集成**: 完整的自动配置和 Starter 支持
- 📊 **监控友好**: 提供丰富的统计信息和健康检查

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>io.github.chenygs</groupId>
    <artifactId>im-message-spring-boot3-starter</artifactId>
    <version>*******</version>
</dependency>
```

### 2. 配置文件

```yaml
im:
  enabled: true
  server:
    port: 8080
    host: 0.0.0.0
  websocket:
    path: /ws
    read-idle-time: 60
  session:
    type: local  # local 或 redis
  message:
    persistence: false
  distributed:
    enabled: false
```

### 3. 启动应用

```java
@SpringBootApplication
public class IMApplication {
    public static void main(String[] args) {
        SpringApplication.run(IMApplication.class, args);
    }
}
```

### 4. 客户端连接

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onopen = function() {
    // 发送连接成功消息
    const message = {
        messageType: 'CONNECT_SUCCESS',
        receiverId: 'user123',
        content: 'Connected successfully'
    };
    ws.send(JSON.stringify(message));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
};
```

## 核心架构

### 1. 服务端会话系统 (ServerSession)

新的会话系统采用统一的接口设计，支持本地会话和远程会话：

```java
// 服务端会话接口
public interface ServerSession {
    AttributeKey<Long> KEY_USER_ID = AttributeKey.valueOf("KEY_USER_ID");
    AttributeKey<LocalSession> SESSION_KEY = AttributeKey.valueOf("SESSION_KEY");

    boolean writeAndFlush(ImS2C packet);
}

// 本地会话 - 处理本地连接
@Autowired
private SessionService sessionService;

// 创建本地会话
LocalSession localSession = sessionService.createLocalSession(userId, channel);

// 发送消息
ImS2C message = new ImS2C()
    .setMessageType(ImS2C.MessageTypes.PRIVATE_MESSAGE)
    .setReceiverId(userId)
    .setContent("Hello World");

localSession.writeAndFlush(message);

// 远程会话 - 处理分布式消息
SessionCache sessionCache = new SessionCache()
    .setUserId(userId)
    .setSessionId("session-123")
    .setServerNode(serverNode);

RouterSession routerSession = sessionService.createRouterSession(sessionCache);
routerSession.writeAndFlush(message);
```

### 2. 会话服务 (SessionService)

```java
@Autowired
private SessionService sessionService;

// 发送消息给指定用户（所有设备）
int sentCount = sessionService.sendToUser(userId, message);

// 发送消息给指定用户的指定设备
boolean success = sessionService.sendToUserDevice(userId, deviceId, message);

// 广播消息给所有在线用户
int userCount = sessionService.broadcastToAll(message);

// 获取用户会话信息
List<SessionInfo> sessions = sessionService.getUserSessionInfos(userId);

// 断开指定会话
boolean disconnected = sessionService.disconnectSession(userId, sessionId);
```

### 3. 消息处理器 (MessageHandler)

```java
@Component
public class CustomMessageHandler implements MessageHandler {

    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        // 处理自定义消息类型
        return true;
    }

    @Override
    public boolean supports(String messageType) {
        return "CUSTOM_MESSAGE".equals(messageType);
    }
}
```

### 4. 协议适配器 (ProtocolAdapter)

```java
@Component
public class CustomProtocolAdapter implements ProtocolAdapter {
    
    @Override
    public String getProtocolType() {
        return "CUSTOM";
    }
    
    @Override
    public void configurePipeline(ChannelPipeline pipeline) {
        // 配置自定义协议处理器
    }
    
    // 实现其他方法...
}
```

### 5. 会话管理 (SessionManager)

```java
@Autowired
private SessionManager sessionManager;

// 创建通道上下文
ChannelContext context = sessionManager.createChannelContext(channel, "WEBSOCKET");

// 绑定用户和通道
sessionManager.bindUserChannel("user123", context, "device1");

// 获取用户会话上下文
List<ChannelContext> contexts = sessionManager.getUserChannelContexts("user123");

// 获取会话详细信息
ChannelContext context = sessionManager.getChannelContext(channel);
String sessionId = context.getSessionId();
String clientIp = context.getClientIp();
LocalDateTime onlineTime = context.getOnlineTime();

// 获取会话统计
SessionManager.SessionStatistics stats = sessionManager.getSessionStatistics();
long totalSessions = stats.getTotalSessions();
long activeSessions = stats.getActiveSessions();
```

### 6. 消息发送服务 (MessageSendService)

```java
@Autowired
private MessageSendService messageSendService;

// 发送消息给指定用户
Message message = new Message()
    .setMessageType(Message.MessageTypes.PRIVATE_MESSAGE)
    .setReceiverId("user123")
    .setContent("Hello World");

messageSendService.sendToUser("user123", message);

// 广播消息
messageSendService.broadcastToAll(message);
```

## 消息类型

| 类型 | 说明 | 示例 |
|------|------|------|
| HEARTBEAT | 心跳消息 | 保持连接活跃 |
| CONNECT_SUCCESS | 连接成功 | 用户认证成功后发送 |
| CONNECT_FAILED | 连接失败 | 认证失败时发送 |
| PRIVATE_MESSAGE | 私信消息 | 用户间私聊 |
| GROUP_MESSAGE | 群组消息 | 群聊消息 |
| ACK | 确认消息 | 消息送达确认 |
| SYSTEM_NOTIFICATION | 系统通知 | 系统公告等 |
| USER_STATUS | 用户状态 | 在线/离线状态 |
| ERROR | 错误消息 | 错误信息通知 |

## 分布式配置

### 1. 启用分布式

```yaml
im:
  distributed:
    enabled: true
    node-id: ${spring.application.name}-${server.port}
    distributor: redis
  session:
    type: redis
    distributed: true
```

### 2. Redis 配置

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 3. Redisson 配置

```yaml
spring:
  redisson:
    config: |
      singleServerConfig:
        address: "redis://localhost:6379"
        database: 0
```

## API 接口

### 聊天接口

#### 发送私信
```http
POST /api/chat/send
Content-Type: application/json

{
    "senderId": "user1",
    "receiverId": "user2",
    "content": "Hello World"
}
```

#### 广播消息
```http
POST /api/chat/broadcast
Content-Type: application/json

{
    "content": "系统维护通知"
}
```

#### 获取在线用户
```http
GET /api/chat/online-users
```

### 会话管理接口

#### 获取所有会话
```http
GET /api/session/all
```

#### 获取用户会话
```http
GET /api/session/user/{userId}
```

#### 获取会话统计
```http
GET /api/session/statistics
```

#### 获取会话详情
```http
GET /api/session/detail/{sessionId}
```

#### 断开会话
```http
POST /api/session/disconnect/{sessionId}
```

#### 清理无效会话
```http
POST /api/session/cleanup
```

## 自定义扩展

### 1. 自定义消息ID生成器

```java
@Component
public class CustomMessageIdGenerator implements MessageIdGenerator {
    
    @Override
    public String nextId() {
        // 自定义ID生成逻辑
        return UUID.randomUUID().toString();
    }
}
```

### 2. 自定义消息持久化

```java
@Component
public class CustomMessagePersistenceService implements MessagePersistenceService {
    
    @Override
    public void saveMessage(Message message) {
        // 自定义消息存储逻辑
    }
    
    // 实现其他方法...
}
```

### 3. 自定义通道适配器

```java
@Component
public class CustomChannelAdapter implements ChannelAdapter {
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 自定义连接处理逻辑
    }
    
    // 实现其他方法...
}
```

## 监控和统计

```java
@Autowired
private SessionManager sessionManager;

// 获取在线用户数量
long onlineCount = sessionManager.getOnlineUserCount();

// 获取在线用户列表
Set<String> onlineUsers = sessionManager.getOnlineUserIds();

// 检查用户是否在线
boolean isOnline = sessionManager.isUserOnline("user123");
```

## 演示页面

### 聊天演示
启动应用后访问: http://localhost:8080/chat.html

演示页面提供了完整的聊天功能，包括：
- WebSocket 连接/断开
- 发送私信
- 广播消息
- 查看在线用户
- 实时消息接收

### 会话管理
启动应用后访问: http://localhost:8080/session.html

会话管理页面提供了完整的会话监控功能，包括：
- 实时会话统计（总会话数、活跃会话、已认证会话等）
- 会话列表查看（会话ID、用户ID、设备ID、协议类型等）
- 会话详情查看（网络信息、统计信息、扩展属性等）
- 会话管理操作（强制断开、清理无效会话）
- 会话数据导出（CSV格式）

## 注意事项

1. **JDK 版本**: 需要 JDK 21 或更高版本
2. **Spring Boot 版本**: 需要 Spring Boot 3.x
3. **Netty 版本**: 使用 Netty 4.1.115.Final
4. **分布式部署**: 启用分布式功能需要 Redis 支持
5. **消息大小**: 默认最大消息大小为 1MB，可通过配置调整

## 许可证

MIT License
