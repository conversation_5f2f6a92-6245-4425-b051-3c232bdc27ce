package io.github.chenygs.im.core.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 服务端到客户端消息模型
 * 服务端发送给客户端指定用户集的消息包裹
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ImS2C implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 接收者ID
     */
    private Long receiverId;

    /**
     * 消息内容
     */
    private Object content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 消息状态
     */
    private String status;

    /**
     * 扩展属性
     */
    private Map<String, Object> extras;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 目标服务器ID（用于路由）
     */
    private String targetServerId;

    /**
     * 消息类型常量
     */
    public static class MessageTypes {
        public static final String HEARTBEAT = "HEARTBEAT";
        public static final String CONNECT_SUCCESS = "CONNECT_SUCCESS";
        public static final String CONNECT_FAILED = "CONNECT_FAILED";
        public static final String PRIVATE_MESSAGE = "PRIVATE_MESSAGE";
        public static final String GROUP_MESSAGE = "GROUP_MESSAGE";
        public static final String ACK = "ACK";
        public static final String SYSTEM_NOTIFICATION = "SYSTEM_NOTIFICATION";
        public static final String USER_STATUS = "USER_STATUS";
        public static final String ERROR = "ERROR";
    }
}
