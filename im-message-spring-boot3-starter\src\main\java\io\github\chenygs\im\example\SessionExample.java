package io.github.chenygs.im.example;

import io.github.chenygs.im.core.model.ImS2C;
import io.github.chenygs.im.core.session.*;
import io.github.chenygs.im.service.SessionService;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 会话使用示例
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SessionExample {
    
    @Autowired
    private SessionService sessionService;
    
    @Autowired
    private ServerSessionManager sessionManager;
    
    /**
     * 演示本地会话的创建和使用
     */
    public void demonstrateLocalSession(Channel channel) {
        log.info("=== 本地会话演示 ===");
        
        // 1. 创建本地会话
        Long userId = 12345L;
        LocalSession localSession = sessionService.createLocalSession(userId, channel);
        
        log.info("创建本地会话: userId={}, sessionId={}, channelId={}", 
            userId, localSession.getSessionId(), channel.id().asShortText());
        
        // 2. 发送消息
        ImS2C message = new ImS2C()
                .setMessageType(ImS2C.MessageTypes.PRIVATE_MESSAGE)
                .setSenderId(67890L)
                .setReceiverId(userId)
                .setContent("Hello from local session!")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());
        
        boolean success = localSession.writeAndFlush(message);
        log.info("本地会话发送消息: success={}", success);
        
        // 3. 检查会话状态
        log.info("会话状态: active={}", localSession.isActive());
        
        // 4. 通过SessionService发送消息
        int sentCount = sessionService.sendToUser(userId, message);
        log.info("通过SessionService发送消息: sentCount={}", sentCount);
    }
    
    /**
     * 演示远程会话的创建和使用
     */
    public void demonstrateRouterSession() {
        log.info("=== 远程会话演示 ===");
        
        // 1. 创建会话缓存信息
        SessionCache sessionCache = new SessionCache()
                .setUserId(54321L)
                .setSessionId("remote-session-12345")
                .setDeviceId("mobile-device-001")
                .setProtocol("WEBSOCKET")
                .setClientIp("************0")
                .setClientPort(8080)
                .setCreateTime(LocalDateTime.now())
                .setLastActiveTime(LocalDateTime.now())
                .setStatus("ACTIVE");
        
        // 设置服务器节点信息
        SessionCache.ServerNode serverNode = new SessionCache.ServerNode()
                .setServerId("server-node-001")
                .setServerIp("************")
                .setServerPort(8080)
                .setStatus("ACTIVE")
                .setWeight(100)
                .setRegion("us-east-1");
        
        sessionCache.setServerNode(serverNode);
        
        // 2. 创建远程会话
        RouterSession routerSession = sessionService.createRouterSession(sessionCache);
        
        log.info("创建远程会话: userId={}, sessionId={}, serverId={}", 
            routerSession.getUserId(), routerSession.getSessionId(), routerSession.getServerId());
        
        // 3. 发送消息
        ImS2C message = new ImS2C()
                .setMessageType(ImS2C.MessageTypes.SYSTEM_NOTIFICATION)
                .setSenderId(0L) // 系统消息
                .setReceiverId(sessionCache.getUserId())
                .setContent("Hello from router session!")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());
        
        boolean success = routerSession.writeAndFlush(message);
        log.info("远程会话发送消息: success={}", success);
        
        // 4. 检查会话状态
        log.info("会话状态: valid={}", routerSession.isValid());
    }
    
    /**
     * 演示会话管理功能
     */
    public void demonstrateSessionManagement() {
        log.info("=== 会话管理演示 ===");
        
        // 1. 获取会话统计信息
        SessionService.SessionStatistics stats = sessionService.getSessionStatistics();
        log.info("会话统计: totalSessions={}, localSessions={}, routerSessions={}, onlineUsers={}", 
            stats.getTotalSessions(), stats.getLocalSessions(), 
            stats.getRouterSessions(), stats.getOnlineUsers());
        
        // 2. 获取在线用户
        var onlineUserIds = sessionManager.getOnlineUserIds();
        log.info("在线用户: count={}, userIds={}", onlineUserIds.size(), onlineUserIds);
        
        // 3. 检查特定用户是否在线
        Long testUserId = 12345L;
        boolean isOnline = sessionManager.isUserOnline(testUserId);
        log.info("用户 {} 是否在线: {}", testUserId, isOnline);
        
        // 4. 获取用户会话信息
        var userSessions = sessionService.getUserSessionInfos(testUserId);
        log.info("用户 {} 的会话: count={}", testUserId, userSessions.size());
        
        for (SessionService.SessionInfo sessionInfo : userSessions) {
            log.info("  会话: sessionId={}, type={}, active={}", 
                sessionInfo.getSessionId(), sessionInfo.getSessionType(), sessionInfo.isActive());
        }
        
        // 5. 清理无效会话
        sessionService.cleanInactiveSessions();
        log.info("清理无效会话完成");
    }
    
    /**
     * 演示广播消息
     */
    public void demonstrateBroadcast() {
        log.info("=== 广播消息演示 ===");
        
        ImS2C broadcastMessage = new ImS2C()
                .setMessageType(ImS2C.MessageTypes.SYSTEM_NOTIFICATION)
                .setSenderId(0L) // 系统消息
                .setContent("系统维护通知：服务器将在30分钟后重启")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());
        
        int sentCount = sessionService.broadcastToAll(broadcastMessage);
        log.info("广播消息发送完成: sentToUsers={}", sentCount);
    }
    
    /**
     * 演示设备特定消息
     */
    public void demonstrateDeviceSpecificMessage() {
        log.info("=== 设备特定消息演示 ===");
        
        Long userId = 12345L;
        String deviceId = "mobile-device-001";
        
        ImS2C deviceMessage = new ImS2C()
                .setMessageType(ImS2C.MessageTypes.PRIVATE_MESSAGE)
                .setSenderId(67890L)
                .setReceiverId(userId)
                .setDeviceId(deviceId)
                .setContent("这是发送给特定设备的消息")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());
        
        boolean success = sessionService.sendToUserDevice(userId, deviceId, deviceMessage);
        log.info("设备特定消息发送: userId={}, deviceId={}, success={}", userId, deviceId, success);
    }
    
    /**
     * 演示会话断开
     */
    public void demonstrateSessionDisconnect(String sessionId) {
        log.info("=== 会话断开演示 ===");
        
        Long userId = 12345L;
        boolean success = sessionService.disconnectSession(userId, sessionId);
        log.info("断开会话: userId={}, sessionId={}, success={}", userId, sessionId, success);
    }
}
