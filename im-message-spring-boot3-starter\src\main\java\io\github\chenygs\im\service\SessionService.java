package io.github.chenygs.im.service;

import io.github.chenygs.im.core.model.ImS2C;
import io.github.chenygs.im.core.session.*;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 会话服务
 * 统一管理本地会话和远程会话
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SessionService {
    
    @Autowired
    private ServerSessionManager sessionManager;
    
    /**
     * 创建本地会话
     * 
     * @param userId 用户ID
     * @param channel 通道
     * @return 本地会话
     */
    public LocalSession createLocalSession(Long userId, Channel channel) {
        return sessionManager.createLocalSession(userId, channel);
    }
    
    /**
     * 创建远程会话
     * 
     * @param sessionCache 会话缓存信息
     * @return 远程会话
     */
    public RouterSession createRouterSession(SessionCache sessionCache) {
        return sessionManager.createRouterSession(sessionCache);
    }
    
    /**
     * 发送消息给指定用户
     * 
     * @param userId 用户ID
     * @param message 消息
     * @return 发送成功的会话数量
     */
    public int sendToUser(Long userId, ImS2C message) {
        if (userId == null || message == null) {
            log.warn("UserId or message is null, skip sending");
            return 0;
        }
        
        List<ServerSession> sessions = sessionManager.getUserSessions(userId);
        if (sessions.isEmpty()) {
            log.info("User {} is not online", userId);
            return 0;
        }
        
        int successCount = 0;
        for (ServerSession session : sessions) {
            try {
                if (session.writeAndFlush(message)) {
                    successCount++;
                    log.debug("Message sent to user {} via session {}", userId, getSessionId(session));
                } else {
                    log.warn("Failed to send message to user {} via session {}", userId, getSessionId(session));
                }
            } catch (Exception e) {
                log.error("Error sending message to user {} via session {}", userId, getSessionId(session), e);
            }
        }
        
        log.info("Message sent to user {}: {}/{} sessions successful", userId, successCount, sessions.size());
        return successCount;
    }
    
    /**
     * 发送消息给指定用户的指定设备
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean sendToUserDevice(Long userId, String deviceId, ImS2C message) {
        if (userId == null || deviceId == null || message == null) {
            log.warn("UserId, deviceId or message is null, skip sending");
            return false;
        }
        
        ServerSession session = sessionManager.getUserSession(userId, deviceId);
        if (session == null) {
            log.info("User {} device {} is not online", userId, deviceId);
            return false;
        }
        
        try {
            boolean success = session.writeAndFlush(message);
            if (success) {
                log.info("Message sent to user {} device {}", userId, deviceId);
            } else {
                log.warn("Failed to send message to user {} device {}", userId, deviceId);
            }
            return success;
        } catch (Exception e) {
            log.error("Error sending message to user {} device {}", userId, deviceId, e);
            return false;
        }
    }
    
    /**
     * 广播消息给所有在线用户
     * 
     * @param message 消息
     * @return 发送成功的用户数量
     */
    public int broadcastToAll(ImS2C message) {
        if (message == null) {
            log.warn("Message is null, skip broadcasting");
            return 0;
        }
        
        var onlineUserIds = sessionManager.getOnlineUserIds();
        int successCount = 0;
        
        for (Long userId : onlineUserIds) {
            try {
                int userSuccessCount = sendToUser(userId, message);
                if (userSuccessCount > 0) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("Failed to broadcast message to user {}", userId, e);
            }
        }
        
        log.info("Broadcast message sent to {} users out of {}", successCount, onlineUserIds.size());
        return successCount;
    }
    
    /**
     * 断开用户会话
     * 
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 是否断开成功
     */
    public boolean disconnectSession(Long userId, String sessionId) {
        if (userId == null || sessionId == null) {
            return false;
        }
        
        ServerSession session = sessionManager.getSession(sessionId);
        if (session == null) {
            log.warn("Session not found: {}", sessionId);
            return false;
        }
        
        try {
            // 如果是本地会话，关闭通道
            if (session instanceof LocalSession) {
                LocalSession localSession = (LocalSession) session;
                localSession.close();
            }
            
            // 移除会话
            sessionManager.removeSession(userId, sessionId);
            
            log.info("Session disconnected: userId={}, sessionId={}", userId, sessionId);
            return true;
        } catch (Exception e) {
            log.error("Error disconnecting session: userId={}, sessionId={}", userId, sessionId, e);
            return false;
        }
    }
    
    /**
     * 获取用户会话信息
     * 
     * @param userId 用户ID
     * @return 会话信息列表
     */
    public List<SessionInfo> getUserSessionInfos(Long userId) {
        List<ServerSession> sessions = sessionManager.getUserSessions(userId);
        
        return sessions.stream()
                .map(this::convertToSessionInfo)
                .toList();
    }
    
    /**
     * 获取会话统计信息
     * 
     * @return 会话统计信息
     */
    public SessionStatistics getSessionStatistics() {
        SessionStatistics stats = new SessionStatistics();
        stats.setTotalSessions(sessionManager.getTotalSessionCount());
        stats.setLocalSessions(sessionManager.getLocalSessionCount());
        stats.setRouterSessions(sessionManager.getRouterSessionCount());
        stats.setOnlineUsers(sessionManager.getOnlineUserCount());
        return stats;
    }
    
    /**
     * 清理无效会话
     */
    public void cleanInactiveSessions() {
        sessionManager.cleanInactiveSessions();
    }
    
    /**
     * 获取会话ID
     */
    private String getSessionId(ServerSession session) {
        if (session instanceof LocalSession) {
            return ((LocalSession) session).getSessionId();
        } else if (session instanceof RouterSession) {
            return ((RouterSession) session).getSessionId();
        }
        return "unknown";
    }
    
    /**
     * 转换为会话信息
     */
    private SessionInfo convertToSessionInfo(ServerSession session) {
        SessionInfo info = new SessionInfo();
        
        if (session instanceof LocalSession) {
            LocalSession localSession = (LocalSession) session;
            info.setSessionId(localSession.getSessionId())
                .setUserId(localSession.getUserId())
                .setSessionType("LOCAL")
                .setActive(localSession.isActive());
        } else if (session instanceof RouterSession) {
            RouterSession routerSession = (RouterSession) session;
            info.setSessionId(routerSession.getSessionId())
                .setUserId(routerSession.getUserId())
                .setDeviceId(routerSession.getDeviceId())
                .setSessionType("ROUTER")
                .setServerId(routerSession.getServerId())
                .setActive(routerSession.isValid());
        }
        
        return info;
    }
    
    /**
     * 会话信息
     */
    public static class SessionInfo {
        private String sessionId;
        private Long userId;
        private String deviceId;
        private String sessionType;
        private String serverId;
        private boolean active;
        
        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public SessionInfo setSessionId(String sessionId) { this.sessionId = sessionId; return this; }
        
        public Long getUserId() { return userId; }
        public SessionInfo setUserId(Long userId) { this.userId = userId; return this; }
        
        public String getDeviceId() { return deviceId; }
        public SessionInfo setDeviceId(String deviceId) { this.deviceId = deviceId; return this; }
        
        public String getSessionType() { return sessionType; }
        public SessionInfo setSessionType(String sessionType) { this.sessionType = sessionType; return this; }
        
        public String getServerId() { return serverId; }
        public SessionInfo setServerId(String serverId) { this.serverId = serverId; return this; }
        
        public boolean isActive() { return active; }
        public SessionInfo setActive(boolean active) { this.active = active; return this; }
    }
    
    /**
     * 会话统计信息
     */
    public static class SessionStatistics {
        private long totalSessions;
        private long localSessions;
        private long routerSessions;
        private long onlineUsers;
        
        // Getters and Setters
        public long getTotalSessions() { return totalSessions; }
        public void setTotalSessions(long totalSessions) { this.totalSessions = totalSessions; }
        
        public long getLocalSessions() { return localSessions; }
        public void setLocalSessions(long localSessions) { this.localSessions = localSessions; }
        
        public long getRouterSessions() { return routerSessions; }
        public void setRouterSessions(long routerSessions) { this.routerSessions = routerSessions; }
        
        public long getOnlineUsers() { return onlineUsers; }
        public void setOnlineUsers(long onlineUsers) { this.onlineUsers = onlineUsers; }
    }
}
