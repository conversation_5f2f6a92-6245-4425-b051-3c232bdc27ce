<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IM聊天演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .message.sent {
            background: #007bff;
            color: white;
            margin-left: 50px;
        }
        .message.received {
            background: #e9ecef;
            margin-right: 50px;
        }
        .message.system {
            background: #ffc107;
            text-align: center;
            margin: 0 20px;
        }
        .input-area {
            padding: 20px;
            background: #f8f9fa;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input {
            flex: 1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            text-align: center;
            font-size: 14px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>IM聊天演示</h1>
            <div id="status" class="status disconnected">未连接</div>
        </div>
        
        <div id="chatArea" class="chat-area">
            <!-- 消息将在这里显示 -->
        </div>
        
        <div class="input-area">
            <div class="input-group">
                <input type="text" id="userIdInput" placeholder="用户ID" value="user1">
                <button onclick="connect()">连接</button>
                <button onclick="disconnect()">断开</button>
            </div>
            
            <div class="input-group">
                <input type="text" id="receiverInput" placeholder="接收者ID" value="user2">
                <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()">发送</button>
            </div>
            
            <div class="input-group">
                <input type="text" id="broadcastInput" placeholder="广播消息...">
                <button onclick="broadcastMessage()">广播</button>
                <button onclick="getOnlineUsers()">在线用户</button>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let userId = null;

        function connect() {
            userId = document.getElementById('userIdInput').value.trim();
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const wsUrl = `ws://localhost:8080/ws`;
            websocket = new WebSocket(wsUrl);

            websocket.onopen = function() {
                updateStatus('已连接', true);
                addMessage('系统', '连接成功', 'system');
                
                // 发送连接成功消息
                const connectMessage = {
                    messageType: 'CONNECT_SUCCESS',
                    receiverId: userId,
                    content: 'Connected successfully',
                    createTime: new Date().toISOString()
                };
                websocket.send(JSON.stringify(connectMessage));
            };

            websocket.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (e) {
                    console.error('解析消息失败:', e);
                    addMessage('系统', '收到无效消息: ' + event.data, 'system');
                }
            };

            websocket.onclose = function() {
                updateStatus('已断开', false);
                addMessage('系统', '连接已断开', 'system');
            };

            websocket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                addMessage('系统', '连接错误', 'system');
            };
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function sendMessage() {
            const receiverId = document.getElementById('receiverInput').value.trim();
            const content = document.getElementById('messageInput').value.trim();
            
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                alert('请先连接WebSocket');
                return;
            }
            
            if (!receiverId || !content) {
                alert('请输入接收者ID和消息内容');
                return;
            }

            const message = {
                messageType: 'PRIVATE_MESSAGE',
                senderId: userId,
                receiverId: receiverId,
                content: content,
                createTime: new Date().toISOString()
            };

            websocket.send(JSON.stringify(message));
            addMessage(userId, content, 'sent');
            document.getElementById('messageInput').value = '';
        }

        function broadcastMessage() {
            const content = document.getElementById('broadcastInput').value.trim();
            
            if (!content) {
                alert('请输入广播消息内容');
                return;
            }

            fetch('/api/chat/broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ content: content })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage('系统', `广播消息已发送给 ${data.sentCount} 个用户`, 'system');
                } else {
                    addMessage('系统', '广播失败: ' + data.message, 'system');
                }
            })
            .catch(error => {
                console.error('广播失败:', error);
                addMessage('系统', '广播失败', 'system');
            });

            document.getElementById('broadcastInput').value = '';
        }

        function getOnlineUsers() {
            fetch('/api/chat/online-users')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const users = Array.from(data.onlineUsers).join(', ');
                    addMessage('系统', `在线用户 (${data.onlineCount}): ${users}`, 'system');
                } else {
                    addMessage('系统', '获取在线用户失败: ' + data.message, 'system');
                }
            })
            .catch(error => {
                console.error('获取在线用户失败:', error);
                addMessage('系统', '获取在线用户失败', 'system');
            });
        }

        function handleMessage(message) {
            switch (message.messageType) {
                case 'PRIVATE_MESSAGE':
                    addMessage(message.senderId, message.content, 'received');
                    break;
                case 'SYSTEM_NOTIFICATION':
                    addMessage('系统通知', message.content, 'system');
                    break;
                case 'HEARTBEAT':
                    // 心跳消息不显示
                    break;
                case 'ACK':
                    addMessage('系统', '消息已确认', 'system');
                    break;
                case 'ERROR':
                    addMessage('错误', message.content, 'system');
                    break;
                default:
                    addMessage('未知', JSON.stringify(message), 'system');
            }
        }

        function addMessage(sender, content, type) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const time = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>${sender}</strong> [${time}]: ${content}`;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function updateStatus(text, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = text;
            statusDiv.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            // 可以在这里添加自动连接逻辑
        };
    </script>
</body>
</html>
