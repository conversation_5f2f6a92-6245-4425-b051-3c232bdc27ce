package io.github.chenygs.im.util;

import io.netty.channel.Channel;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;

/**
 * 通用工具类
 * 
 * <AUTHOR>
 */
public class G {
    
    /**
     * 判断是否为WebSocket通道
     * 
     * @param channel Netty通道
     * @return 是否为WebSocket通道
     */
    public static boolean isWebSocketChannel(Channel channel) {
        if (channel == null) {
            return false;
        }
        
        // 检查管道中是否包含WebSocket处理器
        return channel.pipeline().get(WebSocketServerProtocolHandler.class) != null ||
               channel.pipeline().get("websocket-protocol") != null;
    }
    
    /**
     * 获取通道的协议类型
     * 
     * @param channel Netty通道
     * @return 协议类型
     */
    public static String getChannelProtocol(Channel channel) {
        if (channel == null) {
            return "UNKNOWN";
        }
        
        if (isWebSocketChannel(channel)) {
            return "WEBSOCKET";
        }
        
        // 可以根据其他特征判断协议类型
        return "TCP";
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @param channel Netty通道
     * @return IP地址
     */
    public static String getClientIp(Channel channel) {
        if (channel == null || channel.remoteAddress() == null) {
            return null;
        }
        
        String address = channel.remoteAddress().toString();
        if (address.startsWith("/")) {
            address = address.substring(1);
        }
        
        int colonIndex = address.lastIndexOf(':');
        if (colonIndex > 0) {
            return address.substring(0, colonIndex);
        }
        
        return address;
    }
    
    /**
     * 获取客户端端口
     * 
     * @param channel Netty通道
     * @return 端口号
     */
    public static Integer getClientPort(Channel channel) {
        if (channel == null || channel.remoteAddress() == null) {
            return null;
        }
        
        String address = channel.remoteAddress().toString();
        if (address.startsWith("/")) {
            address = address.substring(1);
        }
        
        int colonIndex = address.lastIndexOf(':');
        if (colonIndex > 0 && colonIndex < address.length() - 1) {
            try {
                return Integer.parseInt(address.substring(colonIndex + 1));
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
}
