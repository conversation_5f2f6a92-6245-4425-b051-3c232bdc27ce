package io.github.chenygs.im.service;

import io.github.chenygs.im.core.model.Message;

import java.util.List;

/**
 * 消息持久化服务接口
 * 
 * <AUTHOR>
 */
public interface MessagePersistenceService {
    
    /**
     * 保存消息
     * 
     * @param message 消息
     */
    void saveMessage(Message message);
    
    /**
     * 更新消息状态
     * 
     * @param messageId 消息ID
     * @param status 新状态
     */
    void updateMessageStatus(String messageId, Message.MessageStatus status);
    
    /**
     * 根据消息ID获取消息
     * 
     * @param messageId 消息ID
     * @return 消息
     */
    Message getMessage(String messageId);
    
    /**
     * 获取用户的离线消息
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<Message> getOfflineMessages(String userId, int limit);
    
    /**
     * 获取用户的历史消息
     * 
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 消息列表
     */
    List<Message> getHistoryMessages(String userId, int offset, int limit);
    
    /**
     * 删除消息
     * 
     * @param messageId 消息ID
     */
    void deleteMessage(String messageId);
    
    /**
     * 批量删除过期消息
     * 
     * @param expiredDays 过期天数
     * @return 删除的消息数量
     */
    int deleteExpiredMessages(int expiredDays);
}
