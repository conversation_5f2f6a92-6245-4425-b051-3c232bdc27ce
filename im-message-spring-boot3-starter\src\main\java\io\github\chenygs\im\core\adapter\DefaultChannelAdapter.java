package io.github.chenygs.im.core.adapter;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 默认通道适配器实现
 * 统一处理不同协议下的通道交互行为
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultChannelAdapter implements ChannelAdapter {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private ProtocolAdapter protocolAdapter;
    
    @Autowired
    private List<MessageHandler> messageHandlers;
    
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        String protocol = protocolAdapter.getProtocolType();

        // 创建通道上下文
        io.github.chenygs.im.core.session.ChannelContext channelContext =
            sessionManager.createChannelContext(ctx.channel(), protocol);

        log.info("Channel active: {}, sessionId: {}, protocol: {}, client: {}:{}",
            ctx.channel().id().asShortText(),
            channelContext.getSessionId(),
            protocol,
            channelContext.getClientIp(),
            channelContext.getClientPort());

        // 记录连接统计
        recordConnectionStats(ctx, true);

        // 可以在这里实现连接限制、IP白名单等逻辑
        if (!isConnectionAllowed(ctx)) {
            log.warn("Connection not allowed for channel: {}, IP: {}",
                ctx.channel().id().asShortText(), channelContext.getClientIp());
            protocolAdapter.closeConnection(ctx);
            return;
        }

        // 发送连接成功消息（如果需要认证，可以在认证成功后发送）
        // protocolAdapter.sendConnectSuccess(ctx, null);
    }
    
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        io.github.chenygs.im.core.session.ChannelContext channelContext =
            sessionManager.getChannelContext(ctx.channel());

        if (channelContext != null) {
            String userId = channelContext.getUserId();
            String deviceId = channelContext.getDeviceId();
            String sessionId = channelContext.getSessionId();

            log.info("Channel inactive: {}, sessionId: {}, user: {}, device: {}, duration: {} minutes",
                ctx.channel().id().asShortText(),
                sessionId,
                userId,
                deviceId,
                java.time.Duration.between(channelContext.getOnlineTime(), java.time.LocalDateTime.now()).toMinutes());

            // 解绑用户和通道
            if (userId != null) {
                sessionManager.unbindUserChannel(userId, ctx.channel());
            }

            // 记录连接统计
            recordConnectionStats(ctx, false);

            // 可以在这里实现用户下线通知等逻辑
            if (userId != null) {
                notifyUserOffline(userId, deviceId);
            }
        } else {
            log.warn("Channel inactive but no context found: {}", ctx.channel().id().asShortText());
        }
    }
    
    @Override
    public void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
        io.github.chenygs.im.core.session.ChannelContext channelContext =
            sessionManager.getChannelContext(ctx.channel());

        if (channelContext != null) {
            // 更新活跃时间和统计信息
            sessionManager.updateLastActiveTime(ctx.channel());

            // 更新接收消息统计
            if (sessionManager instanceof io.github.chenygs.im.core.session.LocalSessionManager) {
                int messageSize = msg.toString().getBytes().length;
                ((io.github.chenygs.im.core.session.LocalSessionManager) sessionManager)
                    .updateMessageStatistics(ctx.channel(), false, messageSize);
            }
        }

        log.debug("Channel read message: {}, sessionId: {}, type: {}",
            ctx.channel().id().asShortText(),
            channelContext != null ? channelContext.getSessionId() : "unknown",
            msg.getClass().getSimpleName());

        try {
            // 解码消息
            Object decodedMessage = protocolAdapter.decodeMessage(msg);

            // 处理消息
            boolean handled = processMessage(decodedMessage, ctx);

            if (!handled) {
                log.warn("No handler found for message: {} from channel: {}",
                    decodedMessage, ctx.channel().id().asShortText());
            }
        } catch (Exception e) {
            log.error("Error processing message from channel: {}",
                ctx.channel().id().asShortText(), e);

            // 更新错误统计
            if (sessionManager instanceof io.github.chenygs.im.core.session.LocalSessionManager) {
                ((io.github.chenygs.im.core.session.LocalSessionManager) sessionManager)
                    .updateErrorStatistics(ctx.channel());
            }

            // 发送错误消息给客户端
            sendErrorMessage(ctx, "MESSAGE_PROCESS_ERROR", "Failed to process message");
        }
    }
    
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            handleIdleStateEvent(ctx, (IdleStateEvent) evt);
        } else {
            log.debug("User event triggered: {} in channel: {}", 
                evt.getClass().getSimpleName(), ctx.channel().id().asShortText());
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String userId = sessionManager.getUserId(ctx.channel());
        log.error("Exception caught in channel: {}, user: {}", 
            ctx.channel().id().asShortText(), userId, cause);
        
        // 发送错误消息给客户端
        sendErrorMessage(ctx, "INTERNAL_ERROR", "Internal server error");
        
        // 关闭连接
        protocolAdapter.closeConnection(ctx);
    }
    
    @Override
    public void handleIdleStateEvent(ChannelHandlerContext ctx, IdleStateEvent evt) throws Exception {
        String userId = sessionManager.getUserId(ctx.channel());
        log.warn("Idle state event: {} in channel: {}, user: {}", 
            evt.state(), ctx.channel().id().asShortText(), userId);
        
        switch (evt.state()) {
            case READER_IDLE:
                // 读空闲，可能是客户端没有发送心跳
                log.info("Reader idle timeout, closing channel: {}, user: {}", 
                    ctx.channel().id().asShortText(), userId);
                protocolAdapter.closeConnection(ctx);
                break;
            case WRITER_IDLE:
                // 写空闲，发送心跳
                sendHeartbeat(ctx);
                break;
            case ALL_IDLE:
                // 读写都空闲
                log.info("All idle timeout, closing channel: {}, user: {}", 
                    ctx.channel().id().asShortText(), userId);
                protocolAdapter.closeConnection(ctx);
                break;
        }
    }
    
    /**
     * 处理消息
     */
    private boolean processMessage(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }
        
        Message msg = (Message) message;
        String messageType = msg.getMessageType();
        
        if (messageType == null) {
            log.warn("Message type is null from channel: {}", ctx.channel().id().asShortText());
            return false;
        }
        
        // 按优先级排序处理器
        messageHandlers.sort((h1, h2) -> Integer.compare(h1.getOrder(), h2.getOrder()));
        
        // 查找支持该消息类型的处理器
        for (MessageHandler handler : messageHandlers) {
            if (handler.supports(messageType)) {
                try {
                    boolean result = handler.process(message, ctx);
                    if (result) {
                        log.debug("Message processed by handler: {}", handler.getClass().getSimpleName());
                        return true;
                    }
                } catch (Exception e) {
                    log.error("Error in message handler: {}", handler.getClass().getSimpleName(), e);
                    throw e;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检查连接是否被允许
     */
    private boolean isConnectionAllowed(ChannelHandlerContext ctx) {
        // 这里可以实现连接限制逻辑
        // 例如：IP白名单、连接数限制、频率限制等
        return true;
    }
    
    /**
     * 记录连接统计
     */
    private void recordConnectionStats(ChannelHandlerContext ctx, boolean connected) {
        // 这里可以实现连接统计逻辑
        // 例如：记录到监控系统、更新连接计数器等
        log.debug("Connection stats - Channel: {}, Connected: {}", 
            ctx.channel().id().asShortText(), connected);
    }
    
    /**
     * 通知用户下线
     */
    private void notifyUserOffline(String userId, String deviceId) {
        // 这里可以实现用户下线通知逻辑
        // 例如：通知好友、更新用户状态等
        log.debug("User offline notification - User: {}, Device: {}", userId, deviceId);
    }
    
    /**
     * 发送错误消息
     */
    private void sendErrorMessage(ChannelHandlerContext ctx, String errorCode, String errorMessage) {
        try {
            if (protocolAdapter instanceof io.github.chenygs.im.protocol.websocket.WebSocketProtocolAdapter) {
                ((io.github.chenygs.im.protocol.websocket.WebSocketProtocolAdapter) protocolAdapter)
                    .sendError(ctx, errorCode, errorMessage);
            }
        } catch (Exception e) {
            log.error("Failed to send error message to channel: {}", 
                ctx.channel().id().asShortText(), e);
        }
    }
    
    /**
     * 发送心跳
     */
    private void sendHeartbeat(ChannelHandlerContext ctx) {
        try {
            if (protocolAdapter instanceof io.github.chenygs.im.protocol.websocket.WebSocketProtocolAdapter) {
                ((io.github.chenygs.im.protocol.websocket.WebSocketProtocolAdapter) protocolAdapter)
                    .sendHeartbeat(ctx);
            }
        } catch (Exception e) {
            log.error("Failed to send heartbeat to channel: {}", 
                ctx.channel().id().asShortText(), e);
        }
    }
}
