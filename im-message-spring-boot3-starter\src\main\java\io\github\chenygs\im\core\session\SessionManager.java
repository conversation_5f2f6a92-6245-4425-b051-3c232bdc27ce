package io.github.chenygs.im.core.session;

import io.netty.channel.Channel;
import java.util.List;
import java.util.Set;

/**
 * 会话管理器接口
 * 用于管理用户会话和通道绑定
 *
 * <AUTHOR>
 */
public interface SessionManager {

    /**
     * 创建会话上下文
     *
     * @param channel 通道
     * @param protocol 协议类型
     * @return 会话上下文
     */
    ChannelContext createChannelContext(Channel channel, String protocol);

    /**
     * 绑定用户和通道上下文
     *
     * @param userId 用户ID
     * @param channelContext 通道上下文
     * @param deviceId 设备ID（可选，用于多端登录）
     */
    void bindUserChannel(String userId, ChannelContext channelContext, String deviceId);

    /**
     * 解绑用户和通道
     *
     * @param userId 用户ID
     * @param channel 通道
     */
    void unbindUserChannel(String userId, Channel channel);

    /**
     * 根据通道获取会话上下文
     *
     * @param channel 通道
     * @return 会话上下文
     */
    ChannelContext getChannelContext(Channel channel);

    /**
     * 根据会话ID获取会话上下文
     *
     * @param sessionId 会话ID
     * @return 会话上下文
     */
    ChannelContext getChannelContext(String sessionId);

    /**
     * 根据用户ID获取所有会话上下文
     *
     * @param userId 用户ID
     * @return 会话上下文列表
     */
    List<ChannelContext> getUserChannelContexts(String userId);

    /**
     * 根据用户ID获取所有通道
     *
     * @param userId 用户ID
     * @return 通道列表
     */
    List<Channel> getUserChannels(String userId);

    /**
     * 根据通道获取用户ID
     *
     * @param channel 通道
     * @return 用户ID
     */
    String getUserId(Channel channel);

    /**
     * 根据通道获取设备ID
     *
     * @param channel 通道
     * @return 设备ID
     */
    String getDeviceId(Channel channel);

    /**
     * 获取所有在线用户ID
     *
     * @return 用户ID集合
     */
    Set<String> getOnlineUserIds();

    /**
     * 检查用户是否在线
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(String userId);

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    long getOnlineUserCount();

    /**
     * 获取所有会话上下文
     *
     * @return 所有会话上下文
     */
    List<ChannelContext> getAllChannelContexts();

    /**
     * 更新会话活跃时间
     *
     * @param channel 通道
     */
    void updateLastActiveTime(Channel channel);

    /**
     * 清理无效的通道
     */
    void cleanInactiveChannels();

    /**
     * 获取会话统计信息
     *
     * @return 会话统计信息
     */
    SessionStatistics getSessionStatistics();

    /**
     * 会话统计信息
     */
    class SessionStatistics {
        private long totalSessions;
        private long activeSessions;
        private long authenticatedSessions;
        private long totalMessages;
        private long totalBytes;

        // Getters and Setters
        public long getTotalSessions() { return totalSessions; }
        public void setTotalSessions(long totalSessions) { this.totalSessions = totalSessions; }

        public long getActiveSessions() { return activeSessions; }
        public void setActiveSessions(long activeSessions) { this.activeSessions = activeSessions; }

        public long getAuthenticatedSessions() { return authenticatedSessions; }
        public void setAuthenticatedSessions(long authenticatedSessions) { this.authenticatedSessions = authenticatedSessions; }

        public long getTotalMessages() { return totalMessages; }
        public void setTotalMessages(long totalMessages) { this.totalMessages = totalMessages; }

        public long getTotalBytes() { return totalBytes; }
        public void setTotalBytes(long totalBytes) { this.totalBytes = totalBytes; }
    }
}
