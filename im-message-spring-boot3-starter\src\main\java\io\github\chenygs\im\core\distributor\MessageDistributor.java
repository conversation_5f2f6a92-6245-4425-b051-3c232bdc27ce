package io.github.chenygs.im.core.distributor;

/**
 * 分布式消息转发器接口
 * 用于操作服务端消息发送给客户端消息或服务端消息发送给另一个服务端消息
 * 
 * <AUTHOR>
 */
public interface MessageDistributor {
    
    /**
     * 发送消息给指定用户
     * 
     * @param userId 用户ID
     * @param message 消息
     * @return 是否发送成功
     */
    boolean sendToUser(String userId, Object message);
    
    /**
     * 发送消息给指定用户的指定设备
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param message 消息
     * @return 是否发送成功
     */
    boolean sendToUserDevice(String userId, String deviceId, Object message);
    
    /**
     * 广播消息给所有在线用户
     * 
     * @param message 消息
     * @return 发送成功的用户数量
     */
    int broadcastToAll(Object message);
    
    /**
     * 发送消息给指定用户列表
     * 
     * @param userIds 用户ID列表
     * @param message 消息
     * @return 发送成功的用户数量
     */
    int sendToUsers(String[] userIds, Object message);
    
    /**
     * 发送消息给其他服务器节点
     * 
     * @param nodeId 节点ID
     * @param message 消息
     * @return 是否发送成功
     */
    boolean sendToNode(String nodeId, Object message);
    
    /**
     * 广播消息给所有服务器节点
     * 
     * @param message 消息
     * @return 发送成功的节点数量
     */
    int broadcastToNodes(Object message);
    
    /**
     * 获取当前节点ID
     * 
     * @return 节点ID
     */
    String getCurrentNodeId();
}
