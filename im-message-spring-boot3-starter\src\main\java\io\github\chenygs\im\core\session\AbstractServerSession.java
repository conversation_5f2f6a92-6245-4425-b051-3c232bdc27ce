package io.github.chenygs.im.core.session;

import io.github.chenygs.im.core.model.ImS2C;

import java.util.UUID;

/**
 * 抽象服务端会话
 * 
 * <AUTHOR>
 */
public abstract class AbstractServerSession implements ServerSession {
    
    @Override
    public boolean writeAndFlush(ImS2C packet) {
        return false;
    }
    
    /**
     * 构建会话ID
     * 
     * @return 会话ID
     */
    public String buildSessionId() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }
}
