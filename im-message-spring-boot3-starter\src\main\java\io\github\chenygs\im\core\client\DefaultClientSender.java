package io.github.chenygs.im.core.client;

import io.github.chenygs.im.core.distributor.MessageDistributor;
import io.github.chenygs.im.core.model.ImS2C;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

/**
 * 默认客户端消息发送器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnMissingBean(ClientSender.class)
public class DefaultClientSender implements ClientSender {
    
    @Autowired(required = false)
    private MessageDistributor messageDistributor;
    
    @Override
    public boolean sendMessage(ImS2C message) {
        if (messageDistributor == null) {
            log.warn("MessageDistributor not available, cannot send remote message");
            return false;
        }
        
        try {
            if (message.getReceiverId() != null) {
                return messageDistributor.sendToUser(message.getReceiverId().toString(), message);
            } else {
                log.warn("Message receiverId is null, cannot send message");
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to send message: {}", message.getMessageId(), e);
            return false;
        }
    }
    
    @Override
    public boolean sendMessage(ImS2C message, String serverId) {
        if (messageDistributor == null) {
            log.warn("MessageDistributor not available, cannot send remote message");
            return false;
        }
        
        try {
            // 设置目标服务器ID
            message.setTargetServerId(serverId);
            return messageDistributor.sendToNode(serverId, message);
        } catch (Exception e) {
            log.error("Failed to send message to server {}: {}", serverId, message.getMessageId(), e);
            return false;
        }
    }
    
    @Override
    public int broadcastMessage(ImS2C message) {
        if (messageDistributor == null) {
            log.warn("MessageDistributor not available, cannot broadcast message");
            return 0;
        }
        
        try {
            return messageDistributor.broadcastToNodes(message);
        } catch (Exception e) {
            log.error("Failed to broadcast message: {}", message.getMessageId(), e);
            return 0;
        }
    }
}
