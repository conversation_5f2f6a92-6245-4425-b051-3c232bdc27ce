package io.github.chenygs.im.core.client;

import io.github.chenygs.im.core.model.ImS2C;

/**
 * 客户端消息发送器接口
 * 用于发送远程消息
 * 
 * <AUTHOR>
 */
public interface ClientSender {
    
    /**
     * 发送消息
     * 
     * @param message 消息
     * @return 是否发送成功
     */
    boolean sendMessage(ImS2C message);
    
    /**
     * 发送消息到指定服务器节点
     * 
     * @param message 消息
     * @param serverId 服务器ID
     * @return 是否发送成功
     */
    boolean sendMessage(ImS2C message, String serverId);
    
    /**
     * 广播消息到所有节点
     * 
     * @param message 消息
     * @return 发送成功的节点数量
     */
    int broadcastMessage(ImS2C message);
}
