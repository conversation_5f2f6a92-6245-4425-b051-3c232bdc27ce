package io.github.chenygs.im.example.controller;

import io.github.chenygs.im.core.model.ImS2C;
import io.github.chenygs.im.service.SessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务端会话管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/server-session")
public class ServerSessionController {
    
    @Autowired
    private SessionService sessionService;
    
    /**
     * 发送消息给指定用户
     */
    @PostMapping("/send-to-user")
    public Map<String, Object> sendToUser(@RequestBody SendToUserRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImS2C message = new ImS2C()
                    .setMessageType(request.getMessageType())
                    .setSenderId(request.getSenderId())
                    .setReceiverId(request.getReceiverId())
                    .setContent(request.getContent())
                    .setCreateTime(LocalDateTime.now())
                    .setSendTime(LocalDateTime.now());
            
            int successCount = sessionService.sendToUser(request.getReceiverId(), message);
            
            result.put("success", successCount > 0);
            result.put("message", "Message sent to " + successCount + " sessions");
            result.put("sessionCount", successCount);
            
        } catch (Exception e) {
            log.error("Error sending message to user", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 发送消息给指定用户的指定设备
     */
    @PostMapping("/send-to-device")
    public Map<String, Object> sendToUserDevice(@RequestBody SendToDeviceRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImS2C message = new ImS2C()
                    .setMessageType(request.getMessageType())
                    .setSenderId(request.getSenderId())
                    .setReceiverId(request.getReceiverId())
                    .setDeviceId(request.getDeviceId())
                    .setContent(request.getContent())
                    .setCreateTime(LocalDateTime.now())
                    .setSendTime(LocalDateTime.now());
            
            boolean success = sessionService.sendToUserDevice(request.getReceiverId(), request.getDeviceId(), message);
            
            result.put("success", success);
            result.put("message", success ? "Message sent successfully" : "Failed to send message");
            
        } catch (Exception e) {
            log.error("Error sending message to user device", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 广播消息给所有在线用户
     */
    @PostMapping("/broadcast")
    public Map<String, Object> broadcastToAll(@RequestBody BroadcastRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImS2C message = new ImS2C()
                    .setMessageType(ImS2C.MessageTypes.SYSTEM_NOTIFICATION)
                    .setSenderId(0L) // 系统消息
                    .setContent(request.getContent())
                    .setCreateTime(LocalDateTime.now())
                    .setSendTime(LocalDateTime.now());
            
            int successCount = sessionService.broadcastToAll(message);
            
            result.put("success", true);
            result.put("message", "Broadcast sent to " + successCount + " users");
            result.put("userCount", successCount);
            
        } catch (Exception e) {
            log.error("Error broadcasting message", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取用户会话信息
     */
    @GetMapping("/user/{userId}/sessions")
    public Map<String, Object> getUserSessions(@PathVariable Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<SessionService.SessionInfo> sessions = sessionService.getUserSessionInfos(userId);
            
            result.put("success", true);
            result.put("userId", userId);
            result.put("sessions", sessions);
            result.put("sessionCount", sessions.size());
            
        } catch (Exception e) {
            log.error("Error getting user sessions for {}", userId, e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 断开用户会话
     */
    @PostMapping("/disconnect")
    public Map<String, Object> disconnectSession(@RequestBody DisconnectRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = sessionService.disconnectSession(request.getUserId(), request.getSessionId());
            
            result.put("success", success);
            result.put("message", success ? "Session disconnected successfully" : "Failed to disconnect session");
            
        } catch (Exception e) {
            log.error("Error disconnecting session", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取会话统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getSessionStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SessionService.SessionStatistics stats = sessionService.getSessionStatistics();
            
            result.put("success", true);
            result.put("totalSessions", stats.getTotalSessions());
            result.put("localSessions", stats.getLocalSessions());
            result.put("routerSessions", stats.getRouterSessions());
            result.put("onlineUsers", stats.getOnlineUsers());
            
        } catch (Exception e) {
            log.error("Error getting session statistics", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 清理无效会话
     */
    @PostMapping("/cleanup")
    public Map<String, Object> cleanupSessions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            sessionService.cleanInactiveSessions();
            
            result.put("success", true);
            result.put("message", "Session cleanup completed");
            
        } catch (Exception e) {
            log.error("Error cleaning up sessions", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    // 请求模型类
    public static class SendToUserRequest {
        private String messageType;
        private Long senderId;
        private Long receiverId;
        private String content;
        
        // Getters and Setters
        public String getMessageType() { return messageType; }
        public void setMessageType(String messageType) { this.messageType = messageType; }
        
        public Long getSenderId() { return senderId; }
        public void setSenderId(Long senderId) { this.senderId = senderId; }
        
        public Long getReceiverId() { return receiverId; }
        public void setReceiverId(Long receiverId) { this.receiverId = receiverId; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
    
    public static class SendToDeviceRequest extends SendToUserRequest {
        private String deviceId;
        
        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    }
    
    public static class BroadcastRequest {
        private String content;
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
    
    public static class DisconnectRequest {
        private Long userId;
        private String sessionId;
        
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }
}
