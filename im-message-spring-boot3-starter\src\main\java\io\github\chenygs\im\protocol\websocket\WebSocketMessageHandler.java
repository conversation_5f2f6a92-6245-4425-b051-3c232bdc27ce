package io.github.chenygs.im.protocol.websocket;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
public class WebSocketMessageHandler extends SimpleChannelInboundHandler<WebSocketFrame> {
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
        if (frame instanceof TextWebSocketFrame) {
            // 处理文本消息
            TextWebSocketFrame textFrame = (TextWebSocketFrame) frame;
            String text = textFrame.text();
            
            log.debug("Received WebSocket text message from {}: {}", 
                ctx.channel().id().asShortText(), text);
            
            // 将消息传递给下一个处理器
            ctx.fireChannelRead(textFrame);
        } else {
            // 其他类型的WebSocket帧暂不支持
            log.warn("Unsupported WebSocket frame type: {} from channel {}", 
                frame.getClass().getSimpleName(), ctx.channel().id().asShortText());
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("WebSocket message handler exception in channel {}", 
            ctx.channel().id().asShortText(), cause);
        ctx.fireExceptionCaught(cause);
    }
}
