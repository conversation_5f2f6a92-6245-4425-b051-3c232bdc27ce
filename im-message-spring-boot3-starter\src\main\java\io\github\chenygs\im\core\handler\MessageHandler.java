package io.github.chenygs.im.core.handler;

import io.netty.channel.ChannelHandlerContext;

/**
 * 消息处理器接口
 * 用于处理不同类型的消息
 * 
 * <AUTHOR>
 */
public interface MessageHandler {
    
    /**
     * 处理消息
     * 
     * @param message 消息对象
     * @param ctx Netty通道上下文
     * @return 是否处理成功
     * @throws Exception 处理异常
     */
    boolean process(Object message, ChannelHandlerContext ctx) throws Exception;
    
    /**
     * 是否支持该消息类型
     * 
     * @param messageType 消息类型
     * @return 是否支持
     */
    boolean supports(String messageType);
    
    /**
     * 获取处理器优先级，数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getOrder() {
        return Integer.MAX_VALUE;
    }
}
