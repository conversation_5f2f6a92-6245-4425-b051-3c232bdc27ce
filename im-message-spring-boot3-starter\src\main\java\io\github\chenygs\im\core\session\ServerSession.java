package io.github.chenygs.im.core.session;

import io.github.chenygs.im.core.model.ImS2C;
import io.netty.util.AttributeKey;

/**
 * 服务端会话接口
 * 
 * <AUTHOR>
 */
public interface ServerSession {
    
    /**
     * 用户ID属性键
     */
    AttributeKey<Long> KEY_USER_ID = AttributeKey.valueOf("KEY_USER_ID");
    
    /**
     * 会话属性键
     */
    AttributeKey<LocalSession> SESSION_KEY = AttributeKey.valueOf("SESSION_KEY");
    
    /**
     * 写入并刷新消息
     * 
     * @param packet 消息包
     * @return 是否发送成功
     */
    boolean writeAndFlush(ImS2C packet);
}
