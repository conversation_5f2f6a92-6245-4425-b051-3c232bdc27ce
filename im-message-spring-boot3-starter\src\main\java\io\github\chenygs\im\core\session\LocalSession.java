package io.github.chenygs.im.core.session;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.chenygs.im.core.model.ImS2C;
import io.github.chenygs.im.util.G;
import io.github.chenygs.im.util.SpringUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;

import java.io.Serializable;

/**
 * 本地会话实现
 * 用于处理本地连接的消息发送
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class LocalSession extends AbstractServerSession implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * Netty通道
     */
    private Channel channel;
    
    /**
     * 会话ID
     */
    private final String sessionId;
    
    public LocalSession(Long userId, Channel channel) {
        this.userId = userId;
        this.channel = channel;
        this.sessionId = buildSessionId();
    }
    
    @Override
    @SneakyThrows
    public boolean writeAndFlush(ImS2C s2cInfo) {
        if (channel == null || !channel.isActive()) {
            return false;
        }
        
        try {
            if (G.isWebSocketChannel(channel)) {
                // WebSocket协议处理
                ObjectMapper objectMapper = SpringUtil.getBean(ObjectMapper.class);
                String body = objectMapper.writeValueAsString(s2cInfo);
                Object callbackBody = new TextWebSocketFrame(body);
                ChannelFuture sync = channel.writeAndFlush(callbackBody).sync();
                return sync.isSuccess();
            } else {
                // 其他协议处理（TCP等）
                ChannelFuture sync = channel.writeAndFlush(s2cInfo).sync();
                return sync.isSuccess();
            }
        } catch (Exception e) {
            // 记录错误日志
            return false;
        }
    }
    
    /**
     * 检查通道是否活跃
     * 
     * @return 是否活跃
     */
    public boolean isActive() {
        return channel != null && channel.isActive();
    }
    
    /**
     * 关闭会话
     */
    public void close() {
        if (channel != null && channel.isActive()) {
            channel.close();
        }
    }
    
    @Override
    public String toString() {
        return "LocalSession{" +
                "userId=" + userId +
                ", sessionId='" + sessionId + '\'' +
                ", channelId='" + (channel != null ? channel.id().asShortText() : "null") + '\'' +
                ", active=" + isActive() +
                '}';
    }
}
