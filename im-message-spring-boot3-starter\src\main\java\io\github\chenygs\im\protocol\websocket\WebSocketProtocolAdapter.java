package io.github.chenygs.im.protocol.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.chenygs.im.core.exception.IMException;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPipeline;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket协议适配器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketProtocolAdapter implements ProtocolAdapter {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * WebSocket路径
     */
    private static final String WEBSOCKET_PATH = "/ws";
    
    /**
     * 读空闲时间（秒）
     */
    private static final int READ_IDLE_TIME = 60;
    
    /**
     * 写空闲时间（秒）
     */
    private static final int WRITE_IDLE_TIME = 0;
    
    /**
     * 读写空闲时间（秒）
     */
    private static final int ALL_IDLE_TIME = 0;
    
    @Override
    public String getProtocolType() {
        return "WEBSOCKET";
    }
    
    @Override
    public void configurePipeline(ChannelPipeline pipeline) {
        // HTTP编解码器
        pipeline.addLast("http-codec", new HttpServerCodec());
        
        // HTTP对象聚合器
        pipeline.addLast("http-aggregator", new HttpObjectAggregator(65536));
        
        // 分块写处理器
        pipeline.addLast("chunked-write", new ChunkedWriteHandler());
        
        // 空闲状态处理器
        pipeline.addLast("idle-state", new IdleStateHandler(READ_IDLE_TIME, WRITE_IDLE_TIME, ALL_IDLE_TIME, TimeUnit.SECONDS));
        
        // WebSocket协议处理器
        pipeline.addLast("websocket-protocol", new WebSocketServerProtocolHandler(WEBSOCKET_PATH, null, true));
        
        // WebSocket消息处理器
        pipeline.addLast("websocket-handler", new WebSocketMessageHandler());
    }
    
    @Override
    public Object encodeMessage(Object message) {
        try {
            if (message instanceof Message) {
                String json = objectMapper.writeValueAsString(message);
                return new TextWebSocketFrame(json);
            } else if (message instanceof String) {
                return new TextWebSocketFrame((String) message);
            } else {
                String json = objectMapper.writeValueAsString(message);
                return new TextWebSocketFrame(json);
            }
        } catch (Exception e) {
            log.error("Failed to encode message: {}", message, e);
            throw new IMException(IMException.ErrorCodes.MESSAGE_SEND_FAILED, "Failed to encode message", e);
        }
    }
    
    @Override
    public Object decodeMessage(Object message) {
        try {
            if (message instanceof TextWebSocketFrame) {
                String text = ((TextWebSocketFrame) message).text();
                return objectMapper.readValue(text, Message.class);
            } else if (message instanceof String) {
                return objectMapper.readValue((String) message, Message.class);
            } else {
                log.warn("Unsupported message type: {}", message.getClass());
                return message;
            }
        } catch (Exception e) {
            log.error("Failed to decode message: {}", message, e);
            // 如果解码失败，返回原始消息
            return message;
        }
    }
    
    @Override
    public void sendMessage(ChannelHandlerContext ctx, Object message) {
        if (ctx == null || !ctx.channel().isActive()) {
            log.warn("Channel is not active, skip sending message");
            return;
        }
        
        try {
            Object encodedMessage = encodeMessage(message);
            ctx.writeAndFlush(encodedMessage);
            log.debug("Message sent to channel {}: {}", ctx.channel().id().asShortText(), message);
        } catch (Exception e) {
            log.error("Failed to send message to channel {}: {}", ctx.channel().id().asShortText(), message, e);
            throw new IMException(IMException.ErrorCodes.MESSAGE_SEND_FAILED, "Failed to send message", e);
        }
    }
    
    @Override
    public void closeConnection(ChannelHandlerContext ctx) {
        if (ctx != null && ctx.channel().isActive()) {
            ctx.close();
            log.info("WebSocket connection closed: {}", ctx.channel().id().asShortText());
        }
    }
    
    @Override
    public String getClientIp(ChannelHandlerContext ctx) {
        if (ctx == null || ctx.channel() == null) {
            return null;
        }
        
        try {
            InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
            return socketAddress.getAddress().getHostAddress();
        } catch (Exception e) {
            log.warn("Failed to get client IP from channel {}", ctx.channel().id().asShortText(), e);
            return null;
        }
    }
    
    /**
     * 发送心跳消息
     */
    public void sendHeartbeat(ChannelHandlerContext ctx) {
        Message heartbeat = new Message()
                .setMessageType(Message.MessageTypes.HEARTBEAT)
                .setContent("ping")
                .setCreateTime(java.time.LocalDateTime.now());
        
        sendMessage(ctx, heartbeat);
    }
    
    /**
     * 发送连接成功消息
     */
    public void sendConnectSuccess(ChannelHandlerContext ctx, String userId) {
        Message connectSuccess = new Message()
                .setMessageType(Message.MessageTypes.CONNECT_SUCCESS)
                .setReceiverId(userId)
                .setContent("Connected successfully")
                .setCreateTime(java.time.LocalDateTime.now());
        
        sendMessage(ctx, connectSuccess);
    }
    
    /**
     * 发送连接失败消息
     */
    public void sendConnectFailed(ChannelHandlerContext ctx, String reason) {
        Message connectFailed = new Message()
                .setMessageType(Message.MessageTypes.CONNECT_FAILED)
                .setContent(reason)
                .setCreateTime(java.time.LocalDateTime.now());
        
        sendMessage(ctx, connectFailed);
    }
    
    /**
     * 发送错误消息
     */
    public void sendError(ChannelHandlerContext ctx, String errorCode, String errorMessage) {
        Message error = new Message()
                .setMessageType(Message.MessageTypes.ERROR)
                .setContent(errorMessage)
                .setCreateTime(java.time.LocalDateTime.now());
        
        if (error.getExtras() == null) {
            error.setExtras(new java.util.HashMap<>());
        }
        error.getExtras().put("errorCode", errorCode);
        
        sendMessage(ctx, error);
    }
}
