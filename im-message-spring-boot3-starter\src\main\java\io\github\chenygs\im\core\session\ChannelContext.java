package io.github.chenygs.im.core.session;

import io.netty.channel.Channel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通道上下文信息
 * 维护Channel的完整会话信息
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChannelContext {
    
    /**
     * 会话ID（全局唯一）
     */
    private String sessionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * Netty通道
     */
    private Channel channel;
    
    /**
     * 协议类型（WebSocket、TCP、UDP等）
     */
    private String protocol;
    
    /**
     * 客户端IP地址
     */
    private String clientIp;
    
    /**
     * 客户端端口
     */
    private Integer clientPort;
    
    /**
     * 服务端IP地址
     */
    private String serverIp;
    
    /**
     * 服务端端口
     */
    private Integer serverPort;
    
    /**
     * 上线时间
     */
    private LocalDateTime onlineTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 连接状态
     */
    private ConnectionStatus status;
    
    /**
     * 用户代理信息（如浏览器信息）
     */
    private String userAgent;
    
    /**
     * 认证状态
     */
    private AuthStatus authStatus;
    
    /**
     * 认证时间
     */
    private LocalDateTime authTime;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 统计信息
     */
    private SessionStatistics statistics;
    
    /**
     * 节点ID（分布式环境下的服务器节点标识）
     */
    private String nodeId;
    
    /**
     * 连接来源（如：web、mobile、desktop等）
     */
    private String source;
    
    /**
     * 版本信息
     */
    private String version;
    
    public ChannelContext() {
        this.attributes = new ConcurrentHashMap<>();
        this.statistics = new SessionStatistics();
        this.status = ConnectionStatus.CONNECTING;
        this.authStatus = AuthStatus.UNAUTHENTICATED;
        this.onlineTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        this.attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) this.attributes.get(key);
    }
    
    /**
     * 移除属性
     */
    public void removeAttribute(String key) {
        this.attributes.remove(key);
    }
    
    /**
     * 检查通道是否活跃
     */
    public boolean isActive() {
        return channel != null && channel.isActive();
    }
    
    /**
     * 检查是否已认证
     */
    public boolean isAuthenticated() {
        return authStatus == AuthStatus.AUTHENTICATED;
    }
    
    /**
     * 设置认证成功
     */
    public void setAuthenticated(String userId) {
        this.userId = userId;
        this.authStatus = AuthStatus.AUTHENTICATED;
        this.authTime = LocalDateTime.now();
        this.status = ConnectionStatus.CONNECTED;
    }
    
    /**
     * 设置认证失败
     */
    public void setAuthenticationFailed() {
        this.authStatus = AuthStatus.AUTHENTICATION_FAILED;
    }
    
    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        /**
         * 连接中
         */
        CONNECTING,
        
        /**
         * 已连接
         */
        CONNECTED,
        
        /**
         * 断开中
         */
        DISCONNECTING,
        
        /**
         * 已断开
         */
        DISCONNECTED,
        
        /**
         * 连接异常
         */
        ERROR
    }
    
    /**
     * 认证状态枚举
     */
    public enum AuthStatus {
        /**
         * 未认证
         */
        UNAUTHENTICATED,
        
        /**
         * 认证中
         */
        AUTHENTICATING,
        
        /**
         * 认证成功
         */
        AUTHENTICATED,
        
        /**
         * 认证失败
         */
        AUTHENTICATION_FAILED,
        
        /**
         * 认证过期
         */
        AUTHENTICATION_EXPIRED
    }
    
    /**
     * 会话统计信息
     */
    @Data
    public static class SessionStatistics {
        /**
         * 发送消息数量
         */
        private long sentMessageCount = 0;
        
        /**
         * 接收消息数量
         */
        private long receivedMessageCount = 0;
        
        /**
         * 发送字节数
         */
        private long sentBytes = 0;
        
        /**
         * 接收字节数
         */
        private long receivedBytes = 0;
        
        /**
         * 心跳次数
         */
        private long heartbeatCount = 0;
        
        /**
         * 错误次数
         */
        private long errorCount = 0;
        
        /**
         * 最后发送消息时间
         */
        private LocalDateTime lastSentTime;
        
        /**
         * 最后接收消息时间
         */
        private LocalDateTime lastReceivedTime;
        
        /**
         * 增加发送消息统计
         */
        public void incrementSentMessage(int bytes) {
            this.sentMessageCount++;
            this.sentBytes += bytes;
            this.lastSentTime = LocalDateTime.now();
        }
        
        /**
         * 增加接收消息统计
         */
        public void incrementReceivedMessage(int bytes) {
            this.receivedMessageCount++;
            this.receivedBytes += bytes;
            this.lastReceivedTime = LocalDateTime.now();
        }
        
        /**
         * 增加心跳统计
         */
        public void incrementHeartbeat() {
            this.heartbeatCount++;
        }
        
        /**
         * 增加错误统计
         */
        public void incrementError() {
            this.errorCount++;
        }
    }
}
