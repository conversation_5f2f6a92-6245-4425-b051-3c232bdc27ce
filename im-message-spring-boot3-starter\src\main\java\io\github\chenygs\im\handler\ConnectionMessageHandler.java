package io.github.chenygs.im.handler;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 连接消息处理器
 * 处理连接成功和连接失败消息
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ConnectionMessageHandler implements MessageHandler {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }
        
        Message msg = (Message) message;
        String messageType = msg.getMessageType();
        
        if (Message.MessageTypes.CONNECT_SUCCESS.equals(messageType)) {
            handleConnectSuccess(msg, ctx);
        } else if (Message.MessageTypes.CONNECT_FAILED.equals(messageType)) {
            handleConnectFailed(msg, ctx);
        }
        
        return true;
    }
    
    @Override
    public boolean supports(String messageType) {
        return Message.MessageTypes.CONNECT_SUCCESS.equals(messageType) ||
               Message.MessageTypes.CONNECT_FAILED.equals(messageType);
    }
    
    @Override
    public int getOrder() {
        return 2; // 连接消息优先级较高
    }
    
    /**
     * 处理连接成功消息
     */
    private void handleConnectSuccess(Message msg, ChannelHandlerContext ctx) {
        String userId = msg.getReceiverId();
        String deviceId = msg.getDeviceId();

        if (userId != null) {
            // 获取或创建通道上下文
            io.github.chenygs.im.core.session.ChannelContext channelContext =
                sessionManager.getChannelContext(ctx.channel());

            if (channelContext != null) {
                // 绑定用户和通道
                sessionManager.bindUserChannel(userId, channelContext, deviceId);
                log.info("User {} connected successfully, sessionId: {}, channel: {}, device: {}",
                    userId, channelContext.getSessionId(), ctx.channel().id().asShortText(), deviceId);
            } else {
                log.warn("Channel context not found for channel: {}", ctx.channel().id().asShortText());
            }
        } else {
            log.warn("Connect success message missing userId, channel: {}",
                ctx.channel().id().asShortText());
        }
    }
    
    /**
     * 处理连接失败消息
     */
    private void handleConnectFailed(Message msg, ChannelHandlerContext ctx) {
        String reason = (String) msg.getContent();
        log.warn("Connection failed for channel {}: {}", 
            ctx.channel().id().asShortText(), reason);
        
        // 连接失败时关闭通道
        ctx.close();
    }
}
