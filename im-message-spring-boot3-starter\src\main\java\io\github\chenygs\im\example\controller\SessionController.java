package io.github.chenygs.im.example.controller;

import io.github.chenygs.im.core.session.ChannelContext;
import io.github.chenygs.im.core.session.LocalSessionManager;
import io.github.chenygs.im.core.session.SessionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会话管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/session")
public class SessionController {
    
    @Autowired
    private SessionManager sessionManager;
    
    /**
     * 获取所有会话信息
     */
    @GetMapping("/all")
    public Map<String, Object> getAllSessions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ChannelContext> contexts = sessionManager.getAllChannelContexts();
            
            List<Map<String, Object>> sessions = contexts.stream()
                    .map(this::convertToMap)
                    .collect(Collectors.toList());
            
            result.put("success", true);
            result.put("sessions", sessions);
            result.put("totalCount", sessions.size());
            
        } catch (Exception e) {
            log.error("Error getting all sessions", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取指定用户的会话信息
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserSessions(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ChannelContext> contexts = sessionManager.getUserChannelContexts(userId);
            
            List<Map<String, Object>> sessions = contexts.stream()
                    .map(this::convertToMap)
                    .collect(Collectors.toList());
            
            result.put("success", true);
            result.put("userId", userId);
            result.put("sessions", sessions);
            result.put("sessionCount", sessions.size());
            
        } catch (Exception e) {
            log.error("Error getting user sessions for {}", userId, e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取会话统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getSessionStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SessionManager.SessionStatistics stats = sessionManager.getSessionStatistics();
            
            result.put("success", true);
            result.put("totalSessions", stats.getTotalSessions());
            result.put("activeSessions", stats.getActiveSessions());
            result.put("authenticatedSessions", stats.getAuthenticatedSessions());
            result.put("totalMessages", stats.getTotalMessages());
            result.put("totalBytes", stats.getTotalBytes());
            result.put("onlineUserCount", sessionManager.getOnlineUserCount());
            
            // 如果是LocalSessionManager，获取更多统计信息
            if (sessionManager instanceof LocalSessionManager) {
                LocalSessionManager localManager = (LocalSessionManager) sessionManager;
                result.put("activeSessionCount", localManager.getActiveSessionCount());
                result.put("websocketSessions", localManager.getSessionCountByProtocol("WEBSOCKET"));
            }
            
        } catch (Exception e) {
            log.error("Error getting session statistics", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取指定会话的详细信息
     */
    @GetMapping("/detail/{sessionId}")
    public Map<String, Object> getSessionDetail(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChannelContext context = sessionManager.getChannelContext(sessionId);
            
            if (context != null) {
                result.put("success", true);
                result.put("session", convertToDetailMap(context));
            } else {
                result.put("success", false);
                result.put("message", "Session not found");
            }
            
        } catch (Exception e) {
            log.error("Error getting session detail for {}", sessionId, e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 强制断开指定会话
     */
    @PostMapping("/disconnect/{sessionId}")
    public Map<String, Object> disconnectSession(@PathVariable String sessionId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ChannelContext context = sessionManager.getChannelContext(sessionId);
            
            if (context != null && context.isActive()) {
                context.getChannel().close();
                result.put("success", true);
                result.put("message", "Session disconnected successfully");
            } else {
                result.put("success", false);
                result.put("message", "Session not found or already inactive");
            }
            
        } catch (Exception e) {
            log.error("Error disconnecting session {}", sessionId, e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 清理无效会话
     */
    @PostMapping("/cleanup")
    public Map<String, Object> cleanupSessions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int beforeCount = sessionManager.getAllChannelContexts().size();
            sessionManager.cleanInactiveChannels();
            int afterCount = sessionManager.getAllChannelContexts().size();
            
            result.put("success", true);
            result.put("message", "Cleanup completed");
            result.put("beforeCount", beforeCount);
            result.put("afterCount", afterCount);
            result.put("cleanedCount", beforeCount - afterCount);
            
        } catch (Exception e) {
            log.error("Error cleaning up sessions", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 转换ChannelContext为简单Map
     */
    private Map<String, Object> convertToMap(ChannelContext context) {
        Map<String, Object> map = new HashMap<>();
        map.put("sessionId", context.getSessionId());
        map.put("userId", context.getUserId());
        map.put("deviceId", context.getDeviceId());
        map.put("protocol", context.getProtocol());
        map.put("clientIp", context.getClientIp());
        map.put("clientPort", context.getClientPort());
        map.put("status", context.getStatus());
        map.put("authStatus", context.getAuthStatus());
        map.put("onlineTime", context.getOnlineTime() != null ? 
            context.getOnlineTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        map.put("lastActiveTime", context.getLastActiveTime() != null ? 
            context.getLastActiveTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        map.put("isActive", context.isActive());
        map.put("isAuthenticated", context.isAuthenticated());
        return map;
    }
    
    /**
     * 转换ChannelContext为详细Map
     */
    private Map<String, Object> convertToDetailMap(ChannelContext context) {
        Map<String, Object> map = convertToMap(context);
        
        // 添加详细信息
        map.put("serverIp", context.getServerIp());
        map.put("serverPort", context.getServerPort());
        map.put("userAgent", context.getUserAgent());
        map.put("nodeId", context.getNodeId());
        map.put("source", context.getSource());
        map.put("version", context.getVersion());
        map.put("authTime", context.getAuthTime() != null ? 
            context.getAuthTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        
        // 添加统计信息
        if (context.getStatistics() != null) {
            Map<String, Object> stats = new HashMap<>();
            stats.put("sentMessageCount", context.getStatistics().getSentMessageCount());
            stats.put("receivedMessageCount", context.getStatistics().getReceivedMessageCount());
            stats.put("sentBytes", context.getStatistics().getSentBytes());
            stats.put("receivedBytes", context.getStatistics().getReceivedBytes());
            stats.put("heartbeatCount", context.getStatistics().getHeartbeatCount());
            stats.put("errorCount", context.getStatistics().getErrorCount());
            stats.put("lastSentTime", context.getStatistics().getLastSentTime() != null ? 
                context.getStatistics().getLastSentTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            stats.put("lastReceivedTime", context.getStatistics().getLastReceivedTime() != null ? 
                context.getStatistics().getLastReceivedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            map.put("statistics", stats);
        }
        
        // 添加扩展属性
        map.put("attributes", context.getAttributes());
        
        return map;
    }
}
