package io.github.chenygs.im.core.session;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 会话缓存信息
 * 用于远程会话的路由信息
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SessionCache {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 服务器节点信息
     */
    private ServerNode serverNode;
    
    /**
     * 协议类型
     */
    private String protocol;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 客户端端口
     */
    private Integer clientPort;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 会话状态
     */
    private String status;
    
    /**
     * 服务器节点信息
     */
    @Data
    @Accessors(chain = true)
    public static class ServerNode {
        /**
         * 服务器ID
         */
        private String serverId;
        
        /**
         * 服务器IP
         */
        private String serverIp;
        
        /**
         * 服务器端口
         */
        private Integer serverPort;
        
        /**
         * 服务器状态
         */
        private String status;
        
        /**
         * 权重
         */
        private Integer weight;
        
        /**
         * 区域
         */
        private String region;
    }
}
