package io.github.chenygs.im.core.session;

import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 默认服务端会话管理器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultServerSessionManager implements ServerSessionManager {
    
    /**
     * 用户ID -> 会话列表映射
     */
    private final Map<Long, List<ServerSession>> userSessionMap = new ConcurrentHashMap<>();
    
    /**
     * 会话ID -> 会话映射
     */
    private final Map<String, ServerSession> sessionMap = new ConcurrentHashMap<>();
    
    /**
     * 通道 -> 本地会话映射
     */
    private final Map<Channel, LocalSession> channelSessionMap = new ConcurrentHashMap<>();
    
    @Override
    public LocalSession createLocalSession(Long userId, Channel channel) {
        LocalSession session = new LocalSession(userId, channel);
        
        // 设置通道属性
        channel.attr(ServerSession.KEY_USER_ID).set(userId);
        channel.attr(ServerSession.SESSION_KEY).set(session);
        
        // 添加到映射
        addSession(session);
        channelSessionMap.put(channel, session);
        
        log.info("Created local session: userId={}, sessionId={}, channel={}", 
            userId, session.getSessionId(), channel.id().asShortText());
        
        return session;
    }
    
    @Override
    public RouterSession createRouterSession(SessionCache sessionCache) {
        RouterSession session = new RouterSession(sessionCache);
        
        // 添加到映射
        addSession(session);
        
        log.info("Created router session: userId={}, sessionId={}, serverId={}", 
            sessionCache.getUserId(), sessionCache.getSessionId(), 
            sessionCache.getServerNode() != null ? sessionCache.getServerNode().getServerId() : "unknown");
        
        return session;
    }
    
    @Override
    public void addSession(ServerSession session) {
        if (session == null) {
            return;
        }
        
        Long userId = null;
        String sessionId = null;
        
        if (session instanceof LocalSession) {
            LocalSession localSession = (LocalSession) session;
            userId = localSession.getUserId();
            sessionId = localSession.getSessionId();
        } else if (session instanceof RouterSession) {
            RouterSession routerSession = (RouterSession) session;
            userId = routerSession.getUserId();
            sessionId = routerSession.getSessionId();
        }
        
        if (userId != null && sessionId != null) {
            // 添加到用户会话映射
            userSessionMap.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(session);
            
            // 添加到会话映射
            sessionMap.put(sessionId, session);
            
            log.debug("Added session: userId={}, sessionId={}, type={}", 
                userId, sessionId, session.getClass().getSimpleName());
        }
    }
    
    @Override
    public void removeSession(Long userId, String sessionId) {
        if (userId == null || sessionId == null) {
            return;
        }
        
        // 从会话映射中移除
        ServerSession session = sessionMap.remove(sessionId);
        
        if (session != null) {
            // 从用户会话映射中移除
            List<ServerSession> userSessions = userSessionMap.get(userId);
            if (userSessions != null) {
                userSessions.remove(session);
                if (userSessions.isEmpty()) {
                    userSessionMap.remove(userId);
                }
            }
            
            // 如果是本地会话，还需要从通道映射中移除
            if (session instanceof LocalSession) {
                LocalSession localSession = (LocalSession) session;
                channelSessionMap.remove(localSession.getChannel());
                
                // 清理通道属性
                if (localSession.getChannel() != null) {
                    localSession.getChannel().attr(ServerSession.KEY_USER_ID).set(null);
                    localSession.getChannel().attr(ServerSession.SESSION_KEY).set(null);
                }
            }
            
            log.info("Removed session: userId={}, sessionId={}, type={}", 
                userId, sessionId, session.getClass().getSimpleName());
        }
    }
    
    @Override
    public List<ServerSession> getUserSessions(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        
        List<ServerSession> sessions = userSessionMap.get(userId);
        if (sessions == null) {
            return Collections.emptyList();
        }
        
        // 过滤掉无效的会话
        List<ServerSession> validSessions = sessions.stream()
                .filter(this::isSessionValid)
                .collect(Collectors.toList());
        
        // 如果有无效会话，更新映射
        if (validSessions.size() != sessions.size()) {
            if (validSessions.isEmpty()) {
                userSessionMap.remove(userId);
            } else {
                userSessionMap.put(userId, new CopyOnWriteArrayList<>(validSessions));
            }
            
            // 清理无效会话
            sessions.stream()
                    .filter(session -> !isSessionValid(session))
                    .forEach(session -> {
                        String sessionId = getSessionId(session);
                        if (sessionId != null) {
                            sessionMap.remove(sessionId);
                        }
                    });
        }
        
        return new ArrayList<>(validSessions);
    }
    
    @Override
    public ServerSession getUserSession(Long userId, String deviceId) {
        List<ServerSession> sessions = getUserSessions(userId);
        
        return sessions.stream()
                .filter(session -> {
                    String sessionDeviceId = getSessionDeviceId(session);
                    return Objects.equals(deviceId, sessionDeviceId);
                })
                .findFirst()
                .orElse(null);
    }
    
    @Override
    public ServerSession getSession(String sessionId) {
        return sessionMap.get(sessionId);
    }
    
    @Override
    public LocalSession getLocalSession(Channel channel) {
        return channelSessionMap.get(channel);
    }
    
    @Override
    public Set<Long> getOnlineUserIds() {
        cleanInactiveSessions();
        return new HashSet<>(userSessionMap.keySet());
    }
    
    @Override
    public boolean isUserOnline(Long userId) {
        if (userId == null) {
            return false;
        }
        
        List<ServerSession> sessions = getUserSessions(userId);
        return !sessions.isEmpty();
    }
    
    @Override
    public long getOnlineUserCount() {
        cleanInactiveSessions();
        return userSessionMap.size();
    }
    
    @Override
    public long getTotalSessionCount() {
        return sessionMap.size();
    }
    
    @Override
    public long getLocalSessionCount() {
        return sessionMap.values().stream()
                .filter(session -> session instanceof LocalSession)
                .count();
    }
    
    @Override
    public long getRouterSessionCount() {
        return sessionMap.values().stream()
                .filter(session -> session instanceof RouterSession)
                .count();
    }
    
    @Override
    public void cleanInactiveSessions() {
        Iterator<Map.Entry<Long, List<ServerSession>>> userIterator = userSessionMap.entrySet().iterator();
        
        while (userIterator.hasNext()) {
            Map.Entry<Long, List<ServerSession>> entry = userIterator.next();
            Long userId = entry.getKey();
            List<ServerSession> sessions = entry.getValue();
            
            // 过滤有效会话
            List<ServerSession> validSessions = sessions.stream()
                    .filter(this::isSessionValid)
                    .collect(Collectors.toList());
            
            if (validSessions.isEmpty()) {
                // 用户所有会话都无效，移除用户
                userIterator.remove();
                log.debug("Removed user {} with no valid sessions", userId);
            } else if (validSessions.size() != sessions.size()) {
                // 有部分会话无效，更新会话列表
                entry.setValue(new CopyOnWriteArrayList<>(validSessions));
                log.debug("Updated user {} valid sessions: {}", userId, validSessions.size());
            }
            
            // 清理无效的会话映射
            sessions.stream()
                    .filter(session -> !isSessionValid(session))
                    .forEach(session -> {
                        String sessionId = getSessionId(session);
                        if (sessionId != null) {
                            sessionMap.remove(sessionId);
                            
                            // 如果是本地会话，清理通道映射
                            if (session instanceof LocalSession) {
                                LocalSession localSession = (LocalSession) session;
                                channelSessionMap.remove(localSession.getChannel());
                            }
                            
                            log.debug("Cleaned invalid session: {}", sessionId);
                        }
                    });
        }
    }
    
    @Override
    public List<ServerSession> getAllSessions() {
        return new ArrayList<>(sessionMap.values());
    }
    
    /**
     * 检查会话是否有效
     */
    private boolean isSessionValid(ServerSession session) {
        if (session instanceof LocalSession) {
            LocalSession localSession = (LocalSession) session;
            return localSession.isActive();
        } else if (session instanceof RouterSession) {
            RouterSession routerSession = (RouterSession) session;
            return routerSession.isValid();
        }
        return false;
    }
    
    /**
     * 获取会话ID
     */
    private String getSessionId(ServerSession session) {
        if (session instanceof LocalSession) {
            return ((LocalSession) session).getSessionId();
        } else if (session instanceof RouterSession) {
            return ((RouterSession) session).getSessionId();
        }
        return null;
    }
    
    /**
     * 获取会话设备ID
     */
    private String getSessionDeviceId(ServerSession session) {
        if (session instanceof LocalSession) {
            // 本地会话的设备ID需要从通道属性或其他地方获取
            return null; // 这里需要根据实际情况实现
        } else if (session instanceof RouterSession) {
            return ((RouterSession) session).getDeviceId();
        }
        return null;
    }
}
