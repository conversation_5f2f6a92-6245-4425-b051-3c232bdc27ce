package io.github.chenygs.im.service;

import io.github.chenygs.im.core.distributor.MessageDistributor;
import io.github.chenygs.im.core.exception.IMException;
import io.github.chenygs.im.core.id.MessageIdGenerator;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 消息发送服务
 * 统一的消息发送入口，包含消息持久化功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageSendService {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private ProtocolAdapter protocolAdapter;
    
    @Autowired
    private MessageIdGenerator messageIdGenerator;
    
    @Autowired(required = false)
    private MessageDistributor messageDistributor;
    
    @Autowired(required = false)
    private MessagePersistenceService messagePersistenceService;
    
    /**
     * 发送消息给指定用户
     * 
     * @param userId 用户ID
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean sendToUser(String userId, Object message) {
        if (userId == null || message == null) {
            log.warn("UserId or message is null, skip sending");
            return false;
        }
        
        try {
            // 预处理消息
            Message processedMessage = preprocessMessage(message, userId);
            
            // 持久化消息（待发消息）
            persistMessage(processedMessage, Message.MessageStatus.PENDING);
            
            // 获取用户的所有通道
            List<Channel> channels = sessionManager.getUserChannels(userId);
            
            if (channels.isEmpty()) {
                log.info("User {} is not online, message will be stored for offline delivery", userId);
                
                // 尝试通过分布式转发器发送
                if (messageDistributor != null) {
                    boolean sent = messageDistributor.sendToUser(userId, processedMessage);
                    if (sent) {
                        updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.SENT);
                        return true;
                    }
                }
                
                // 用户不在线，消息已持久化，等待用户上线后推送
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.FAILED);
                return false;
            }
            
            // 发送给用户的所有设备
            boolean allSent = true;
            for (Channel channel : channels) {
                try {
                    sendToChannel(channel, processedMessage);
                    log.debug("Message sent to user {} channel {}", userId, channel.id().asShortText());
                } catch (Exception e) {
                    log.error("Failed to send message to user {} channel {}", 
                        userId, channel.id().asShortText(), e);
                    allSent = false;
                }
            }
            
            // 更新消息状态
            if (allSent) {
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.SENT);
            } else {
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.FAILED);
            }
            
            return allSent;
            
        } catch (Exception e) {
            log.error("Error sending message to user {}", userId, e);
            return false;
        }
    }
    
    /**
     * 发送消息给指定用户的指定设备
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param message 消息
     * @return 是否发送成功
     */
    public boolean sendToUserDevice(String userId, String deviceId, Object message) {
        if (userId == null || deviceId == null || message == null) {
            log.warn("UserId, deviceId or message is null, skip sending");
            return false;
        }
        
        try {
            // 预处理消息
            Message processedMessage = preprocessMessage(message, userId);
            processedMessage.setDeviceId(deviceId);
            
            // 持久化消息
            persistMessage(processedMessage, Message.MessageStatus.PENDING);
            
            // 获取指定设备的通道
            Channel channel = getChannelByDevice(userId, deviceId);
            
            if (channel == null) {
                log.info("User {} device {} is not online", userId, deviceId);
                
                // 尝试通过分布式转发器发送
                if (messageDistributor != null) {
                    boolean sent = messageDistributor.sendToUserDevice(userId, deviceId, processedMessage);
                    if (sent) {
                        updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.SENT);
                        return true;
                    }
                }
                
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.FAILED);
                return false;
            }
            
            // 发送消息
            sendToChannel(channel, processedMessage);
            updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.SENT);
            
            log.info("Message sent to user {} device {}", userId, deviceId);
            return true;
            
        } catch (Exception e) {
            log.error("Error sending message to user {} device {}", userId, deviceId, e);
            return false;
        }
    }
    
    /**
     * 发送消息给指定通道
     * 
     * @param channel 通道
     * @param message 消息
     */
    public void sendToChannel(Channel channel, Object message) {
        if (channel == null || !channel.isActive()) {
            throw new IMException(IMException.ErrorCodes.CHANNEL_NOT_FOUND, "Channel is not active");
        }
        
        if (message == null) {
            throw new IllegalArgumentException("Message cannot be null");
        }
        
        try {
            protocolAdapter.sendMessage(channel.pipeline().lastContext(), message);
            log.debug("Message sent to channel {}", channel.id().asShortText());
        } catch (Exception e) {
            log.error("Failed to send message to channel {}", channel.id().asShortText(), e);
            throw new IMException(IMException.ErrorCodes.MESSAGE_SEND_FAILED, 
                "Failed to send message to channel", e);
        }
    }
    
    /**
     * 广播消息给所有在线用户
     * 
     * @param message 消息
     * @return 发送成功的用户数量
     */
    public int broadcastToAll(Object message) {
        if (message == null) {
            log.warn("Message is null, skip broadcasting");
            return 0;
        }
        
        try {
            // 预处理消息
            Message processedMessage = preprocessMessage(message, null);
            
            // 持久化消息
            persistMessage(processedMessage, Message.MessageStatus.PENDING);
            
            // 获取所有在线用户
            var onlineUserIds = sessionManager.getOnlineUserIds();
            
            int successCount = 0;
            for (String userId : onlineUserIds) {
                try {
                    if (sendToUser(userId, processedMessage)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("Failed to broadcast message to user {}", userId, e);
                }
            }
            
            log.info("Broadcast message sent to {} users out of {}", successCount, onlineUserIds.size());
            
            // 更新消息状态
            if (successCount > 0) {
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.SENT);
            } else {
                updateMessageStatus(processedMessage.getMessageId(), Message.MessageStatus.FAILED);
            }
            
            return successCount;
            
        } catch (Exception e) {
            log.error("Error broadcasting message", e);
            return 0;
        }
    }
    
    /**
     * 预处理消息
     */
    private Message preprocessMessage(Object message, String receiverId) {
        Message processedMessage;
        
        if (message instanceof Message) {
            processedMessage = (Message) message;
        } else {
            // 将其他类型的消息包装成Message对象
            processedMessage = new Message()
                .setContent(message)
                .setMessageType(Message.MessageTypes.SYSTEM_NOTIFICATION);
        }
        
        // 设置消息ID（如果没有）
        if (processedMessage.getMessageId() == null) {
            processedMessage.setMessageId(messageIdGenerator.nextId());
        }
        
        // 设置接收者ID
        if (receiverId != null) {
            processedMessage.setReceiverId(receiverId);
        }
        
        // 设置时间戳
        if (processedMessage.getCreateTime() == null) {
            processedMessage.setCreateTime(LocalDateTime.now());
        }
        processedMessage.setSendTime(LocalDateTime.now());
        
        return processedMessage;
    }
    
    /**
     * 持久化消息
     */
    private void persistMessage(Message message, Message.MessageStatus status) {
        if (messagePersistenceService != null) {
            try {
                message.setStatus(status);
                messagePersistenceService.saveMessage(message);
                log.debug("Message persisted: {}", message.getMessageId());
            } catch (Exception e) {
                log.error("Failed to persist message: {}", message.getMessageId(), e);
            }
        }
    }
    
    /**
     * 更新消息状态
     */
    private void updateMessageStatus(String messageId, Message.MessageStatus status) {
        if (messagePersistenceService != null) {
            try {
                messagePersistenceService.updateMessageStatus(messageId, status);
                log.debug("Message status updated: {} -> {}", messageId, status);
            } catch (Exception e) {
                log.error("Failed to update message status: {} -> {}", messageId, status, e);
            }
        }
    }
    
    /**
     * 根据设备ID获取通道
     */
    private Channel getChannelByDevice(String userId, String deviceId) {
        if (sessionManager instanceof io.github.chenygs.im.core.session.LocalSessionManager) {
            return ((io.github.chenygs.im.core.session.LocalSessionManager) sessionManager)
                .getUserChannelByDevice(userId, deviceId);
        }
        
        // 如果不是LocalSessionManager，遍历所有通道查找
        List<Channel> channels = sessionManager.getUserChannels(userId);
        return channels.stream()
            .filter(channel -> deviceId.equals(sessionManager.getDeviceId(channel)))
            .findFirst()
            .orElse(null);
    }
}
