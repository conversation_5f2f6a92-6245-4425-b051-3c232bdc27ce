package io.github.chenygs.im.core.adapter;

import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleStateEvent;

/**
 * 通道适配器接口
 * 用于操作不同协议下的通道交互行为
 * 
 * <AUTHOR>
 */
public interface ChannelAdapter {
    
    /**
     * 通道激活时调用
     * 
     * @param ctx 通道上下文
     * @throws Exception 异常
     */
    void channelActive(ChannelHandlerContext ctx) throws Exception;
    
    /**
     * 通道非激活时调用
     * 
     * @param ctx 通道上下文
     * @throws Exception 异常
     */
    void channelInactive(ChannelHandlerContext ctx) throws Exception;
    
    /**
     * 读取消息时调用
     * 
     * @param ctx 通道上下文
     * @param msg 消息
     * @throws Exception 异常
     */
    void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception;
    
    /**
     * 用户事件触发时调用
     * 
     * @param ctx 通道上下文
     * @param evt 事件
     * @throws Exception 异常
     */
    void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception;
    
    /**
     * 异常捕获时调用
     * 
     * @param ctx 通道上下文
     * @param cause 异常原因
     * @throws Exception 异常
     */
    void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception;
    
    /**
     * 处理空闲状态事件
     * 
     * @param ctx 通道上下文
     * @param evt 空闲状态事件
     * @throws Exception 异常
     */
    default void handleIdleStateEvent(ChannelHandlerContext ctx, IdleStateEvent evt) throws Exception {
        // 默认实现：关闭连接
        ctx.close();
    }
}
