package io.github.chenygs.im.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * IM系统配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "im")
public class IMProperties {
    
    /**
     * 是否启用IM系统
     */
    private boolean enabled = true;
    
    /**
     * 服务器配置
     */
    private Server server = new Server();
    
    /**
     * WebSocket配置
     */
    private WebSocket websocket = new WebSocket();
    
    /**
     * 会话管理配置
     */
    private Session session = new Session();
    
    /**
     * 消息配置
     */
    private Message message = new Message();
    
    /**
     * 分布式配置
     */
    private Distributed distributed = new Distributed();
    
    @Data
    public static class Server {
        /**
         * 服务器端口
         */
        private int port = 8080;
        
        /**
         * 绑定地址
         */
        private String host = "0.0.0.0";
        
        /**
         * Boss线程数
         */
        private int bossThreads = 1;
        
        /**
         * Worker线程数
         */
        private int workerThreads = 0; // 0表示使用默认值
        
        /**
         * 是否启用TCP_NODELAY
         */
        private boolean tcpNodelay = true;
        
        /**
         * 是否启用SO_KEEPALIVE
         */
        private boolean keepAlive = true;
        
        /**
         * SO_BACKLOG大小
         */
        private int backlog = 128;
    }
    
    @Data
    public static class WebSocket {
        /**
         * WebSocket路径
         */
        private String path = "/ws";
        
        /**
         * 最大帧大小
         */
        private int maxFrameSize = 65536;
        
        /**
         * 是否启用压缩
         */
        private boolean compression = true;
        
        /**
         * 读空闲时间（秒）
         */
        private int readIdleTime = 60;
        
        /**
         * 写空闲时间（秒）
         */
        private int writeIdleTime = 0;
        
        /**
         * 读写空闲时间（秒）
         */
        private int allIdleTime = 0;
    }
    
    @Data
    public static class Session {
        /**
         * 会话管理器类型：local, redis
         */
        private String type = "local";
        
        /**
         * 是否启用分布式会话
         */
        private boolean distributed = false;
        
        /**
         * 会话清理间隔（秒）
         */
        private int cleanupInterval = 300;
        
        /**
         * 会话超时时间（秒）
         */
        private int timeout = 3600;
    }
    
    @Data
    public static class Message {
        /**
         * 消息ID生成器类型：default, snowflake
         */
        private String idGenerator = "default";
        
        /**
         * 是否启用消息持久化
         */
        private boolean persistence = false;
        
        /**
         * 消息最大大小（字节）
         */
        private int maxSize = 1048576; // 1MB
        
        /**
         * 消息过期时间（天）
         */
        private int expireDays = 30;
        
        /**
         * 离线消息最大数量
         */
        private int maxOfflineMessages = 1000;
    }
    
    @Data
    public static class Distributed {
        /**
         * 是否启用分布式
         */
        private boolean enabled = false;
        
        /**
         * 节点ID
         */
        private String nodeId;
        
        /**
         * 消息转发器类型：redis
         */
        private String distributor = "redis";
        
        /**
         * Redis配置
         */
        private Redis redis = new Redis();
        
        @Data
        public static class Redis {
            /**
             * 用户消息主题前缀
             */
            private String userMessageTopicPrefix = "im:user:message:";
            
            /**
             * 节点消息主题前缀
             */
            private String nodeMessageTopicPrefix = "im:node:message:";
            
            /**
             * 广播消息主题
             */
            private String broadcastMessageTopic = "im:broadcast:message";
            
            /**
             * 在线用户集合键
             */
            private String onlineUsersKey = "im:session:online_users";
            
            /**
             * 用户会话键前缀
             */
            private String userSessionKeyPrefix = "im:session:user:";
            
            /**
             * 通道信息键前缀
             */
            private String channelInfoKeyPrefix = "im:session:channel:";
        }
    }
}
