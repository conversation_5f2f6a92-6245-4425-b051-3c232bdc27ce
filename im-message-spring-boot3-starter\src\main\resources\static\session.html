<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IM会话管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats-area {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .controls button {
            margin-right: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .sessions-area {
            padding: 20px;
        }
        .session-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .session-table th,
        .session-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .session-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .session-table tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-connected {
            background: #d4edda;
            color: #155724;
        }
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .status-authenticated {
            background: #d1ecf1;
            color: #0c5460;
        }
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>IM会话管理</h1>
            <p>实时监控和管理即时通讯会话</p>
        </div>
        
        <div class="stats-area">
            <div class="stats-grid" id="statsGrid">
                <!-- 统计信息将在这里显示 -->
            </div>
        </div>
        
        <div class="controls">
            <button onclick="refreshData()">刷新数据</button>
            <button onclick="cleanupSessions()">清理无效会话</button>
            <button onclick="exportSessions()">导出会话</button>
        </div>
        
        <div class="sessions-area">
            <h3>活跃会话列表</h3>
            <div id="sessionsContainer">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <script>
        let sessionsData = [];

        // 页面加载时初始化
        window.onload = function() {
            refreshData();
            // 每30秒自动刷新
            setInterval(refreshData, 30000);
        };

        // 刷新数据
        function refreshData() {
            loadStatistics();
            loadSessions();
        }

        // 加载统计信息
        function loadStatistics() {
            fetch('/api/session/statistics')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayStatistics(data);
                } else {
                    console.error('Failed to load statistics:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading statistics:', error);
            });
        }

        // 加载会话列表
        function loadSessions() {
            fetch('/api/session/all')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    sessionsData = data.sessions;
                    displaySessions(sessionsData);
                } else {
                    document.getElementById('sessionsContainer').innerHTML = 
                        '<div class="loading">加载失败: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error loading sessions:', error);
                document.getElementById('sessionsContainer').innerHTML = 
                    '<div class="loading">加载失败: ' + error.message + '</div>';
            });
        }

        // 显示统计信息
        function displayStatistics(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.totalSessions || 0}</div>
                    <div class="stat-label">总会话数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.activeSessions || 0}</div>
                    <div class="stat-label">活跃会话</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.authenticatedSessions || 0}</div>
                    <div class="stat-label">已认证会话</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.onlineUserCount || 0}</div>
                    <div class="stat-label">在线用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${formatNumber(stats.totalMessages || 0)}</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${formatBytes(stats.totalBytes || 0)}</div>
                    <div class="stat-label">总流量</div>
                </div>
            `;
        }

        // 显示会话列表
        function displaySessions(sessions) {
            const container = document.getElementById('sessionsContainer');
            
            if (sessions.length === 0) {
                container.innerHTML = '<div class="loading">暂无活跃会话</div>';
                return;
            }

            const table = `
                <table class="session-table">
                    <thead>
                        <tr>
                            <th>会话ID</th>
                            <th>用户ID</th>
                            <th>设备ID</th>
                            <th>协议</th>
                            <th>客户端IP</th>
                            <th>状态</th>
                            <th>认证状态</th>
                            <th>上线时间</th>
                            <th>最后活跃</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${sessions.map(session => `
                            <tr>
                                <td title="${session.sessionId}">${session.sessionId.substring(0, 8)}...</td>
                                <td>${session.userId || '-'}</td>
                                <td>${session.deviceId || '-'}</td>
                                <td>${session.protocol || '-'}</td>
                                <td>${session.clientIp || '-'}:${session.clientPort || '-'}</td>
                                <td>
                                    <span class="status-badge ${getStatusClass(session.status)}">
                                        ${session.status || 'UNKNOWN'}
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge ${getAuthStatusClass(session.authStatus)}">
                                        ${session.authStatus || 'UNKNOWN'}
                                    </span>
                                </td>
                                <td>${session.onlineTime || '-'}</td>
                                <td>${session.lastActiveTime || '-'}</td>
                                <td>
                                    <button class="action-btn btn-info" onclick="viewSessionDetail('${session.sessionId}')">详情</button>
                                    <button class="action-btn btn-danger" onclick="disconnectSession('${session.sessionId}')">断开</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 'CONNECTED': return 'status-connected';
                case 'DISCONNECTED': return 'status-disconnected';
                default: return '';
            }
        }

        // 获取认证状态样式类
        function getAuthStatusClass(authStatus) {
            switch (authStatus) {
                case 'AUTHENTICATED': return 'status-authenticated';
                case 'UNAUTHENTICATED': return 'status-disconnected';
                default: return '';
            }
        }

        // 查看会话详情
        function viewSessionDetail(sessionId) {
            fetch(`/api/session/detail/${sessionId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('会话详情:\n' + JSON.stringify(data.session, null, 2));
                } else {
                    alert('获取会话详情失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('获取会话详情失败: ' + error.message);
            });
        }

        // 断开会话
        function disconnectSession(sessionId) {
            if (!confirm('确定要断开这个会话吗？')) {
                return;
            }

            fetch(`/api/session/disconnect/${sessionId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('会话已断开');
                    refreshData();
                } else {
                    alert('断开会话失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('断开会话失败: ' + error.message);
            });
        }

        // 清理无效会话
        function cleanupSessions() {
            fetch('/api/session/cleanup', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`清理完成，清理了 ${data.cleanedCount} 个无效会话`);
                    refreshData();
                } else {
                    alert('清理失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('清理失败: ' + error.message);
            });
        }

        // 导出会话
        function exportSessions() {
            const csv = convertToCSV(sessionsData);
            downloadCSV(csv, 'sessions.csv');
        }

        // 转换为CSV格式
        function convertToCSV(data) {
            const headers = ['会话ID', '用户ID', '设备ID', '协议', '客户端IP', '状态', '认证状态', '上线时间', '最后活跃'];
            const rows = data.map(session => [
                session.sessionId,
                session.userId || '',
                session.deviceId || '',
                session.protocol || '',
                `${session.clientIp || ''}:${session.clientPort || ''}`,
                session.status || '',
                session.authStatus || '',
                session.onlineTime || '',
                session.lastActiveTime || ''
            ]);
            
            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        // 下载CSV文件
        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
