package io.github.chenygs.im.distributor;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.chenygs.im.core.distributor.MessageDistributor;
import io.github.chenygs.im.core.exception.IMException;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.service.MessageSendService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 基于Redisson的分布式消息转发器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnClass(RedissonClient.class)
public class RedissonMessageDistributor implements MessageDistributor {
    
    @Autowired(required = false)
    private RedissonClient redissonClient;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MessageSendService messageSendService;
    
    /**
     * 当前节点ID
     */
    private String currentNodeId;
    
    /**
     * 用户消息主题前缀
     */
    private static final String USER_MESSAGE_TOPIC_PREFIX = "im:user:message:";
    
    /**
     * 节点消息主题前缀
     */
    private static final String NODE_MESSAGE_TOPIC_PREFIX = "im:node:message:";
    
    /**
     * 广播消息主题
     */
    private static final String BROADCAST_MESSAGE_TOPIC = "im:broadcast:message";
    
    @PostConstruct
    public void init() {
        if (redissonClient == null) {
            log.warn("RedissonClient not available, distributed message distributor disabled");
            return;
        }
        
        // 生成当前节点ID
        currentNodeId = generateNodeId();
        log.info("Message distributor initialized with node ID: {}", currentNodeId);
        
        // 订阅节点消息主题
        subscribeNodeMessages();
        
        // 订阅广播消息主题
        subscribeBroadcastMessages();
    }
    
    @Override
    public boolean sendToUser(String userId, Object message) {
        if (redissonClient == null) {
            log.warn("RedissonClient not available, cannot send distributed message");
            return false;
        }
        
        if (userId == null || message == null) {
            return false;
        }
        
        try {
            // 发布消息到用户主题
            RTopic topic = redissonClient.getTopic(USER_MESSAGE_TOPIC_PREFIX + userId);
            
            DistributedMessage distributedMessage = new DistributedMessage()
                .setSourceNodeId(currentNodeId)
                .setTargetUserId(userId)
                .setMessage(message)
                .setMessageType(DistributedMessage.MessageType.USER_MESSAGE)
                .setTimestamp(System.currentTimeMillis());
            
            String messageJson = objectMapper.writeValueAsString(distributedMessage);
            long receiverCount = topic.publish(messageJson);
            
            log.debug("Distributed message sent to user {}, receivers: {}", userId, receiverCount);
            return receiverCount > 0;
            
        } catch (Exception e) {
            log.error("Failed to send distributed message to user {}", userId, e);
            return false;
        }
    }
    
    @Override
    public boolean sendToUserDevice(String userId, String deviceId, Object message) {
        if (redissonClient == null) {
            return false;
        }
        
        if (userId == null || deviceId == null || message == null) {
            return false;
        }
        
        try {
            // 发布消息到用户设备主题
            RTopic topic = redissonClient.getTopic(USER_MESSAGE_TOPIC_PREFIX + userId + ":" + deviceId);
            
            DistributedMessage distributedMessage = new DistributedMessage()
                .setSourceNodeId(currentNodeId)
                .setTargetUserId(userId)
                .setTargetDeviceId(deviceId)
                .setMessage(message)
                .setMessageType(DistributedMessage.MessageType.USER_DEVICE_MESSAGE)
                .setTimestamp(System.currentTimeMillis());
            
            String messageJson = objectMapper.writeValueAsString(distributedMessage);
            long receiverCount = topic.publish(messageJson);
            
            log.debug("Distributed message sent to user {} device {}, receivers: {}", 
                userId, deviceId, receiverCount);
            return receiverCount > 0;
            
        } catch (Exception e) {
            log.error("Failed to send distributed message to user {} device {}", userId, deviceId, e);
            return false;
        }
    }
    
    @Override
    public int broadcastToAll(Object message) {
        if (redissonClient == null) {
            return 0;
        }
        
        if (message == null) {
            return 0;
        }
        
        try {
            RTopic topic = redissonClient.getTopic(BROADCAST_MESSAGE_TOPIC);
            
            DistributedMessage distributedMessage = new DistributedMessage()
                .setSourceNodeId(currentNodeId)
                .setMessage(message)
                .setMessageType(DistributedMessage.MessageType.BROADCAST_MESSAGE)
                .setTimestamp(System.currentTimeMillis());
            
            String messageJson = objectMapper.writeValueAsString(distributedMessage);
            long receiverCount = topic.publish(messageJson);
            
            log.info("Broadcast message sent to {} receivers", receiverCount);
            return (int) receiverCount;
            
        } catch (Exception e) {
            log.error("Failed to broadcast distributed message", e);
            return 0;
        }
    }
    
    @Override
    public int sendToUsers(String[] userIds, Object message) {
        if (userIds == null || userIds.length == 0 || message == null) {
            return 0;
        }
        
        int successCount = 0;
        for (String userId : userIds) {
            if (sendToUser(userId, message)) {
                successCount++;
            }
        }
        
        return successCount;
    }
    
    @Override
    public boolean sendToNode(String nodeId, Object message) {
        if (redissonClient == null || nodeId == null || message == null) {
            return false;
        }
        
        try {
            RTopic topic = redissonClient.getTopic(NODE_MESSAGE_TOPIC_PREFIX + nodeId);
            
            DistributedMessage distributedMessage = new DistributedMessage()
                .setSourceNodeId(currentNodeId)
                .setTargetNodeId(nodeId)
                .setMessage(message)
                .setMessageType(DistributedMessage.MessageType.NODE_MESSAGE)
                .setTimestamp(System.currentTimeMillis());
            
            String messageJson = objectMapper.writeValueAsString(distributedMessage);
            long receiverCount = topic.publish(messageJson);
            
            log.debug("Message sent to node {}, receivers: {}", nodeId, receiverCount);
            return receiverCount > 0;
            
        } catch (Exception e) {
            log.error("Failed to send message to node {}", nodeId, e);
            return false;
        }
    }
    
    @Override
    public int broadcastToNodes(Object message) {
        if (redissonClient == null || message == null) {
            return 0;
        }
        
        try {
            RTopic topic = redissonClient.getTopic("im:nodes:broadcast");
            
            DistributedMessage distributedMessage = new DistributedMessage()
                .setSourceNodeId(currentNodeId)
                .setMessage(message)
                .setMessageType(DistributedMessage.MessageType.NODE_BROADCAST_MESSAGE)
                .setTimestamp(System.currentTimeMillis());
            
            String messageJson = objectMapper.writeValueAsString(distributedMessage);
            long receiverCount = topic.publish(messageJson);
            
            log.info("Broadcast message sent to {} nodes", receiverCount);
            return (int) receiverCount;
            
        } catch (Exception e) {
            log.error("Failed to broadcast message to nodes", e);
            return 0;
        }
    }
    
    @Override
    public String getCurrentNodeId() {
        return currentNodeId;
    }
    
    /**
     * 生成节点ID
     */
    private String generateNodeId() {
        try {
            String hostName = InetAddress.getLocalHost().getHostName();
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            return hostName + "-" + uuid;
        } catch (Exception e) {
            return "node-" + UUID.randomUUID().toString().substring(0, 8);
        }
    }
    
    /**
     * 订阅节点消息
     */
    private void subscribeNodeMessages() {
        RTopic topic = redissonClient.getTopic(NODE_MESSAGE_TOPIC_PREFIX + currentNodeId);
        topic.addListener(String.class, (channel, message) -> {
            try {
                DistributedMessage distributedMessage = objectMapper.readValue(message, DistributedMessage.class);
                handleNodeMessage(distributedMessage);
            } catch (Exception e) {
                log.error("Failed to handle node message: {}", message, e);
            }
        });
        
        log.info("Subscribed to node message topic: {}", NODE_MESSAGE_TOPIC_PREFIX + currentNodeId);
    }
    
    /**
     * 订阅广播消息
     */
    private void subscribeBroadcastMessages() {
        RTopic topic = redissonClient.getTopic(BROADCAST_MESSAGE_TOPIC);
        topic.addListener(String.class, (channel, message) -> {
            try {
                DistributedMessage distributedMessage = objectMapper.readValue(message, DistributedMessage.class);
                
                // 忽略自己发送的消息
                if (!currentNodeId.equals(distributedMessage.getSourceNodeId())) {
                    handleBroadcastMessage(distributedMessage);
                }
            } catch (Exception e) {
                log.error("Failed to handle broadcast message: {}", message, e);
            }
        });
        
        log.info("Subscribed to broadcast message topic: {}", BROADCAST_MESSAGE_TOPIC);
    }
    
    /**
     * 处理节点消息
     */
    private void handleNodeMessage(DistributedMessage distributedMessage) {
        log.debug("Received node message from {}: {}", 
            distributedMessage.getSourceNodeId(), distributedMessage.getMessageType());
        
        // 这里可以实现节点间的特殊消息处理逻辑
        // 例如：集群管理、负载均衡、健康检查等
    }
    
    /**
     * 处理广播消息
     */
    private void handleBroadcastMessage(DistributedMessage distributedMessage) {
        log.debug("Received broadcast message from {}", distributedMessage.getSourceNodeId());
        
        try {
            // 广播给本节点的所有在线用户
            messageSendService.broadcastToAll(distributedMessage.getMessage());
        } catch (Exception e) {
            log.error("Failed to handle broadcast message", e);
        }
    }
}
