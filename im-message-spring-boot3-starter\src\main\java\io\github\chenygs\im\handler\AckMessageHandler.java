package io.github.chenygs.im.handler;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ACK确认消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AckMessageHandler implements MessageHandler {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }
        
        Message msg = (Message) message;
        String userId = sessionManager.getUserId(ctx.channel());
        
        if (userId == null) {
            log.warn("User not authenticated for ACK message, channel: {}", ctx.channel().id().asShortText());
            return false;
        }
        
        log.debug("Processing ACK message from user {}", userId);
        
        // 获取原始消息ID
        String originalMessageId = null;
        if (msg.getExtras() != null) {
            originalMessageId = (String) msg.getExtras().get("originalMessageId");
        }
        
        if (originalMessageId != null) {
            log.info("Received ACK for message {} from user {}", originalMessageId, userId);
            
            // 这里可以实现消息状态更新逻辑
            // 例如：更新数据库中消息的状态为已确认
            updateMessageStatus(originalMessageId, Message.MessageStatus.DELIVERED);
        } else {
            log.warn("ACK message missing originalMessageId from user {}", userId);
        }
        
        return true;
    }
    
    @Override
    public boolean supports(String messageType) {
        return Message.MessageTypes.ACK.equals(messageType);
    }
    
    @Override
    public int getOrder() {
        return 5;
    }
    
    /**
     * 更新消息状态
     * 这里是示例实现，实际项目中应该更新数据库
     */
    private void updateMessageStatus(String messageId, Message.MessageStatus status) {
        // TODO: 实现消息状态更新逻辑
        // 例如：messageRepository.updateStatus(messageId, status);
        log.debug("Message {} status updated to {}", messageId, status);
    }
}
