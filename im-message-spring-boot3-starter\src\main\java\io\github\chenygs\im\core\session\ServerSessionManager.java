package io.github.chenygs.im.core.session;

import io.netty.channel.Channel;

import java.util.List;
import java.util.Set;

/**
 * 服务端会话管理器接口
 * 
 * <AUTHOR>
 */
public interface ServerSessionManager {
    
    /**
     * 创建本地会话
     * 
     * @param userId 用户ID
     * @param channel 通道
     * @return 本地会话
     */
    LocalSession createLocalSession(Long userId, Channel channel);
    
    /**
     * 创建路由会话
     * 
     * @param sessionCache 会话缓存信息
     * @return 路由会话
     */
    RouterSession createRouterSession(SessionCache sessionCache);
    
    /**
     * 添加会话
     * 
     * @param session 会话
     */
    void addSession(ServerSession session);
    
    /**
     * 移除会话
     * 
     * @param userId 用户ID
     * @param sessionId 会话ID
     */
    void removeSession(Long userId, String sessionId);
    
    /**
     * 根据用户ID获取所有会话
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ServerSession> getUserSessions(Long userId);
    
    /**
     * 根据用户ID和设备ID获取会话
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 会话
     */
    ServerSession getUserSession(Long userId, String deviceId);
    
    /**
     * 根据会话ID获取会话
     * 
     * @param sessionId 会话ID
     * @return 会话
     */
    ServerSession getSession(String sessionId);
    
    /**
     * 根据通道获取会话
     * 
     * @param channel 通道
     * @return 会话
     */
    LocalSession getLocalSession(Channel channel);
    
    /**
     * 获取所有在线用户ID
     * 
     * @return 用户ID集合
     */
    Set<Long> getOnlineUserIds();
    
    /**
     * 检查用户是否在线
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(Long userId);
    
    /**
     * 获取在线用户数量
     * 
     * @return 在线用户数量
     */
    long getOnlineUserCount();
    
    /**
     * 获取所有会话数量
     * 
     * @return 会话数量
     */
    long getTotalSessionCount();
    
    /**
     * 获取本地会话数量
     * 
     * @return 本地会话数量
     */
    long getLocalSessionCount();
    
    /**
     * 获取路由会话数量
     * 
     * @return 路由会话数量
     */
    long getRouterSessionCount();
    
    /**
     * 清理无效会话
     */
    void cleanInactiveSessions();
    
    /**
     * 获取所有会话
     * 
     * @return 所有会话
     */
    List<ServerSession> getAllSessions();
}
