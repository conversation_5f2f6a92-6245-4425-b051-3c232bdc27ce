package io.github.chenygs.im.example.controller;

import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.session.SessionManager;
import io.github.chenygs.im.service.MessageSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 聊天控制器示例
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    @Autowired
    private MessageSendService messageSendService;
    
    @Autowired
    private SessionManager sessionManager;
    
    /**
     * 发送私信
     */
    @PostMapping("/send")
    public Map<String, Object> sendMessage(@RequestBody SendMessageRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Message message = new Message()
                .setMessageType(Message.MessageTypes.PRIVATE_MESSAGE)
                .setSenderId(request.getSenderId())
                .setReceiverId(request.getReceiverId())
                .setContent(request.getContent())
                .setCreateTime(LocalDateTime.now());
            
            boolean sent = messageSendService.sendToUser(request.getReceiverId(), message);
            
            result.put("success", sent);
            result.put("message", sent ? "Message sent successfully" : "Failed to send message");
            result.put("messageId", message.getMessageId());
            
        } catch (Exception e) {
            log.error("Error sending message", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 广播消息
     */
    @PostMapping("/broadcast")
    public Map<String, Object> broadcastMessage(@RequestBody BroadcastRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Message message = new Message()
                .setMessageType(Message.MessageTypes.SYSTEM_NOTIFICATION)
                .setSenderId("SYSTEM")
                .setContent(request.getContent())
                .setCreateTime(LocalDateTime.now());
            
            int sentCount = messageSendService.broadcastToAll(message);
            
            result.put("success", true);
            result.put("message", "Broadcast sent to " + sentCount + " users");
            result.put("sentCount", sentCount);
            
        } catch (Exception e) {
            log.error("Error broadcasting message", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取在线用户
     */
    @GetMapping("/online-users")
    public Map<String, Object> getOnlineUsers() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            var onlineUsers = sessionManager.getOnlineUserIds();
            long onlineCount = sessionManager.getOnlineUserCount();
            
            result.put("success", true);
            result.put("onlineUsers", onlineUsers);
            result.put("onlineCount", onlineCount);
            
        } catch (Exception e) {
            log.error("Error getting online users", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查用户是否在线
     */
    @GetMapping("/user/{userId}/online")
    public Map<String, Object> isUserOnline(@PathVariable String userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean online = sessionManager.isUserOnline(userId);
            
            result.put("success", true);
            result.put("userId", userId);
            result.put("online", online);
            
        } catch (Exception e) {
            log.error("Error checking user online status", e);
            result.put("success", false);
            result.put("message", "Error: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 发送消息请求
     */
    public static class SendMessageRequest {
        private String senderId;
        private String receiverId;
        private String content;
        
        // Getters and Setters
        public String getSenderId() { return senderId; }
        public void setSenderId(String senderId) { this.senderId = senderId; }
        
        public String getReceiverId() { return receiverId; }
        public void setReceiverId(String receiverId) { this.receiverId = receiverId; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
    
    /**
     * 广播请求
     */
    public static class BroadcastRequest {
        private String content;
        
        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
}
