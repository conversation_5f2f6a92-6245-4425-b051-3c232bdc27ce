package io.github.chenygs.im.handler;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 系统通知消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SystemNotificationHandler implements MessageHandler {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }
        
        Message msg = (Message) message;
        String userId = sessionManager.getUserId(ctx.channel());
        
        log.info("Processing system notification for user {}: {}", userId, msg.getContent());
        
        // 系统通知消息通常是服务端主动发送给客户端的
        // 客户端收到后可能需要进行一些处理，比如显示通知、更新UI等
        
        // 记录通知接收日志
        logNotificationReceived(userId, msg);
        
        // 可以在这里实现通知的持久化存储
        // 例如：notificationRepository.save(notification);
        
        return true;
    }
    
    @Override
    public boolean supports(String messageType) {
        return Message.MessageTypes.SYSTEM_NOTIFICATION.equals(messageType);
    }
    
    @Override
    public int getOrder() {
        return 15;
    }
    
    /**
     * 记录通知接收日志
     */
    private void logNotificationReceived(String userId, Message notification) {
        log.info("System notification received - User: {}, Type: {}, Content: {}", 
            userId, notification.getMessageType(), notification.getContent());
        
        // 这里可以实现更详细的日志记录逻辑
        // 例如：记录到审计日志、统计通知接收率等
    }
}
