package io.github.chenygs.im.core.id;

/**
 * 消息ID生成器接口
 * 用于生成全局唯一、趋势递增的消息ID
 * 
 * <AUTHOR>
 */
public interface MessageIdGenerator {
    
    /**
     * 生成下一个消息ID
     * 
     * @return 消息ID
     */
    String nextId();
    
    /**
     * 生成指定类型的消息ID
     * 
     * @param messageType 消息类型
     * @return 消息ID
     */
    default String nextId(String messageType) {
        return nextId();
    }
    
    /**
     * 批量生成消息ID
     * 
     * @param count 数量
     * @return 消息ID数组
     */
    default String[] nextIds(int count) {
        String[] ids = new String[count];
        for (int i = 0; i < count; i++) {
            ids[i] = nextId();
        }
        return ids;
    }
}
