package io.github.chenygs.im.handler;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.id.MessageIdGenerator;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.session.SessionManager;
import io.github.chenygs.im.service.MessageSendService;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 私信消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class PrivateMessageHandler implements MessageHandler {
    
    @Autowired
    private SessionManager sessionManager;
    
    @Autowired
    private MessageIdGenerator messageIdGenerator;
    
    @Autowired
    private MessageSendService messageSendService;
    
    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }
        
        Message msg = (Message) message;
        String senderId = sessionManager.getUserId(ctx.channel());
        String receiverId = msg.getReceiverId();
        
        if (senderId == null) {
            log.warn("Sender not authenticated, channel: {}", ctx.channel().id().asShortText());
            return false;
        }
        
        if (receiverId == null) {
            log.warn("Receiver ID is null in private message from {}", senderId);
            return false;
        }
        
        log.info("Processing private message from {} to {}", senderId, receiverId);
        
        // 设置消息属性
        msg.setMessageId(messageIdGenerator.nextId())
           .setSenderId(senderId)
           .setCreateTime(LocalDateTime.now())
           .setSendTime(LocalDateTime.now())
           .setStatus(Message.MessageStatus.SENT);
        
        // 发送ACK给发送者
        sendAck(ctx, msg.getMessageId(), senderId);
        
        // 转发消息给接收者
        boolean sent = messageSendService.sendToUser(receiverId, msg);
        
        if (sent) {
            log.info("Private message {} sent from {} to {}", msg.getMessageId(), senderId, receiverId);
        } else {
            log.warn("Failed to send private message {} from {} to {}", msg.getMessageId(), senderId, receiverId);
            // 可以在这里实现离线消息存储
        }
        
        return true;
    }
    
    @Override
    public boolean supports(String messageType) {
        return Message.MessageTypes.PRIVATE_MESSAGE.equals(messageType);
    }
    
    @Override
    public int getOrder() {
        return 10;
    }
    
    /**
     * 发送ACK确认消息
     */
    private void sendAck(ChannelHandlerContext ctx, String messageId, String userId) {
        Message ack = new Message()
                .setMessageId(messageIdGenerator.nextId())
                .setMessageType(Message.MessageTypes.ACK)
                .setReceiverId(userId)
                .setContent("Message received")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());
        
        if (ack.getExtras() == null) {
            ack.setExtras(new java.util.HashMap<>());
        }
        ack.getExtras().put("originalMessageId", messageId);
        
        try {
            messageSendService.sendToChannel(ctx.channel(), ack);
            log.debug("ACK sent for message {} to user {}", messageId, userId);
        } catch (Exception e) {
            log.error("Failed to send ACK for message {} to user {}", messageId, userId, e);
        }
    }
}
