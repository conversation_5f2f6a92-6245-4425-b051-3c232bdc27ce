package io.github.chenygs.im.server;

import io.github.chenygs.im.config.IMProperties;
import io.github.chenygs.im.core.adapter.ChannelAdapter;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * Netty IM服务器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class NettyIMServer implements InitializingBean, DisposableBean {
    
    private final IMProperties properties;
    private final ProtocolAdapter protocolAdapter;
    private final ChannelAdapter channelAdapter;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;
    
    public NettyIMServer(IMProperties properties, 
                        ProtocolAdapter protocolAdapter,
                        ChannelAdapter channelAdapter) {
        this.properties = properties;
        this.protocolAdapter = protocolAdapter;
        this.channelAdapter = channelAdapter;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        start();
    }
    
    @Override
    public void destroy() throws Exception {
        stop();
    }
    
    /**
     * 启动服务器
     */
    public void start() throws Exception {
        IMProperties.Server serverConfig = properties.getServer();
        
        // 创建事件循环组
        bossGroup = new NioEventLoopGroup(serverConfig.getBossThreads());
        
        int workerThreads = serverConfig.getWorkerThreads();
        if (workerThreads <= 0) {
            workerGroup = new NioEventLoopGroup();
        } else {
            workerGroup = new NioEventLoopGroup(workerThreads);
        }
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // 配置协议处理器
                            protocolAdapter.configurePipeline(pipeline);
                            
                            // 添加业务处理器
                            pipeline.addLast("im-handler", new IMChannelHandler(channelAdapter));
                        }
                    })
                    .option(ChannelOption.SO_BACKLOG, serverConfig.getBacklog())
                    .childOption(ChannelOption.SO_KEEPALIVE, serverConfig.isKeepAlive())
                    .childOption(ChannelOption.TCP_NODELAY, serverConfig.isTcpNodelay());
            
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(serverConfig.getHost(), serverConfig.getPort()).sync();
            serverChannel = future.channel();
            
            log.info("IM Server started on {}:{}", serverConfig.getHost(), serverConfig.getPort());
            
            // 异步等待服务器关闭
            serverChannel.closeFuture().addListener((ChannelFutureListener) channelFuture -> {
                log.info("IM Server stopped");
            });
            
        } catch (Exception e) {
            log.error("Failed to start IM Server", e);
            stop();
            throw e;
        }
    }
    
    /**
     * 停止服务器
     */
    public void stop() {
        try {
            if (serverChannel != null) {
                serverChannel.close().sync();
            }
        } catch (Exception e) {
            log.error("Error closing server channel", e);
        }
        
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        
        log.info("IM Server shutdown completed");
    }
    
    /**
     * IM通道处理器
     */
    private static class IMChannelHandler extends SimpleChannelInboundHandler<Object> {
        
        private final ChannelAdapter channelAdapter;
        
        public IMChannelHandler(ChannelAdapter channelAdapter) {
            this.channelAdapter = channelAdapter;
        }
        
        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            channelAdapter.channelActive(ctx);
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            channelAdapter.channelInactive(ctx);
        }
        
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
            channelAdapter.channelRead0(ctx, msg);
        }
        
        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            channelAdapter.userEventTriggered(ctx, evt);
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            channelAdapter.exceptionCaught(ctx, cause);
        }
    }
}
