package io.github.chenygs.im.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息模型
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Message {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 发送者ID
     */
    private String senderId;
    
    /**
     * 接收者ID
     */
    private String receiverId;
    
    /**
     * 消息内容
     */
    private Object content;
    
    /**
     * 消息创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 消息发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;
    
    /**
     * 消息状态
     */
    private MessageStatus status;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extras;
    
    /**
     * 设备ID（用于多端消息）
     */
    private String deviceId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 消息优先级
     */
    private Integer priority;
    
    /**
     * 消息过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;
    
    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        /**
         * 待发送
         */
        PENDING,
        
        /**
         * 已发送
         */
        SENT,
        
        /**
         * 已送达
         */
        DELIVERED,
        
        /**
         * 已读
         */
        READ,
        
        /**
         * 发送失败
         */
        FAILED,
        
        /**
         * 已撤回
         */
        RECALLED
    }
    
    /**
     * 消息类型常量
     */
    public static class MessageTypes {
        /**
         * 心跳消息
         */
        public static final String HEARTBEAT = "HEARTBEAT";
        
        /**
         * 连接成功消息
         */
        public static final String CONNECT_SUCCESS = "CONNECT_SUCCESS";
        
        /**
         * 连接失败消息
         */
        public static final String CONNECT_FAILED = "CONNECT_FAILED";
        
        /**
         * 私信消息
         */
        public static final String PRIVATE_MESSAGE = "PRIVATE_MESSAGE";
        
        /**
         * 群组消息
         */
        public static final String GROUP_MESSAGE = "GROUP_MESSAGE";
        
        /**
         * ACK确认消息
         */
        public static final String ACK = "ACK";
        
        /**
         * 系统通知消息
         */
        public static final String SYSTEM_NOTIFICATION = "SYSTEM_NOTIFICATION";
        
        /**
         * 用户状态消息
         */
        public static final String USER_STATUS = "USER_STATUS";
        
        /**
         * 错误消息
         */
        public static final String ERROR = "ERROR";
    }
}
