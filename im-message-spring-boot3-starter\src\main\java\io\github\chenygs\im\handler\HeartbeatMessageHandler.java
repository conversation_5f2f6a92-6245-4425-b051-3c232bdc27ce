package io.github.chenygs.im.handler;

import io.github.chenygs.im.core.handler.MessageHandler;
import io.github.chenygs.im.core.model.Message;
import io.github.chenygs.im.core.protocol.ProtocolAdapter;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 心跳消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HeartbeatMessageHandler implements MessageHandler {
    
    @Autowired
    private ProtocolAdapter protocolAdapter;
    
    @Override
    public boolean process(Object message, ChannelHandlerContext ctx) throws Exception {
        if (!(message instanceof Message)) {
            return false;
        }

        Message msg = (Message) message;

        // 更新心跳统计
        if (sessionManager instanceof io.github.chenygs.im.core.session.LocalSessionManager) {
            ((io.github.chenygs.im.core.session.LocalSessionManager) sessionManager)
                .updateHeartbeatStatistics(ctx.channel());
        }

        log.debug("Processing heartbeat message from channel {}", ctx.channel().id().asShortText());

        // 创建心跳响应消息
        Message response = new Message()
                .setMessageType(Message.MessageTypes.HEARTBEAT)
                .setContent("pong")
                .setCreateTime(LocalDateTime.now())
                .setSendTime(LocalDateTime.now());

        // 发送心跳响应
        protocolAdapter.sendMessage(ctx, response);

        log.debug("Heartbeat response sent to channel {}", ctx.channel().id().asShortText());
        return true;
    }
    
    @Override
    public boolean supports(String messageType) {
        return Message.MessageTypes.HEARTBEAT.equals(messageType);
    }
    
    @Override
    public int getOrder() {
        return 1; // 心跳消息优先级最高
    }
}
