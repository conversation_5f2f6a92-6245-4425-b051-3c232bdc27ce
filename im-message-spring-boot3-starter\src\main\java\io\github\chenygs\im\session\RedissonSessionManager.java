package io.github.chenygs.im.session;

import io.github.chenygs.im.core.session.SessionManager;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 基于Redisson的分布式会话管理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnClass(RedissonClient.class)
public class RedissonSessionManager implements SessionManager {
    
    @Autowired(required = false)
    private RedissonClient redissonClient;
    
    /**
     * 本地通道映射（通道对象无法序列化，只能存储在本地）
     */
    private final Map<String, Channel> localChannelMap = new ConcurrentHashMap<>();
    
    /**
     * 本地通道到用户ID的映射
     */
    private final Map<Channel, String> localChannelUserMap = new ConcurrentHashMap<>();
    
    /**
     * 用户会话信息Redis键前缀
     */
    private static final String USER_SESSION_KEY_PREFIX = "im:session:user:";
    
    /**
     * 在线用户集合Redis键
     */
    private static final String ONLINE_USERS_KEY = "im:session:online_users";
    
    /**
     * 通道信息Redis键前缀
     */
    private static final String CHANNEL_INFO_KEY_PREFIX = "im:session:channel:";
    
    @Override
    public void bindUserChannel(String userId, Channel channel, String deviceId) {
        if (userId == null || channel == null) {
            log.warn("UserId or channel is null, skip binding");
            return;
        }
        
        String channelId = channel.id().asLongText();
        
        // 本地存储通道对象
        localChannelMap.put(channelId, channel);
        localChannelUserMap.put(channel, userId);
        
        // 设置通道属性
        channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.USER_ID_KEY).set(userId);
        if (deviceId != null) {
            channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.DEVICE_ID_KEY).set(deviceId);
        }
        channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.CONNECT_TIME_KEY).set(System.currentTimeMillis());
        
        if (redissonClient != null) {
            try {
                // 存储用户会话信息到Redis
                RMap<String, SessionInfo> userSessionMap = redissonClient.getMap(USER_SESSION_KEY_PREFIX + userId);
                
                SessionInfo sessionInfo = new SessionInfo()
                    .setChannelId(channelId)
                    .setUserId(userId)
                    .setDeviceId(deviceId)
                    .setConnectTime(System.currentTimeMillis())
                    .setNodeId(getCurrentNodeId());
                
                userSessionMap.put(channelId, sessionInfo);
                
                // 添加到在线用户集合
                RSet<String> onlineUsers = redissonClient.getSet(ONLINE_USERS_KEY);
                onlineUsers.add(userId);
                
                // 存储通道信息
                RMap<String, Object> channelInfoMap = redissonClient.getMap(CHANNEL_INFO_KEY_PREFIX + channelId);
                channelInfoMap.put("userId", userId);
                channelInfoMap.put("deviceId", deviceId);
                channelInfoMap.put("nodeId", getCurrentNodeId());
                channelInfoMap.put("connectTime", System.currentTimeMillis());
                
                log.info("User {} bound to channel {} in distributed session, device: {}", 
                    userId, channelId, deviceId);
                
            } catch (Exception e) {
                log.error("Failed to bind user session in Redis", e);
            }
        }
    }
    
    @Override
    public void unbindUserChannel(String userId, Channel channel) {
        if (userId == null || channel == null) {
            return;
        }
        
        String channelId = channel.id().asLongText();
        
        // 清理本地映射
        localChannelMap.remove(channelId);
        localChannelUserMap.remove(channel);
        
        // 清理通道属性
        channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.USER_ID_KEY).set(null);
        channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.DEVICE_ID_KEY).set(null);
        channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.CONNECT_TIME_KEY).set(null);
        
        if (redissonClient != null) {
            try {
                // 从Redis中移除会话信息
                RMap<String, SessionInfo> userSessionMap = redissonClient.getMap(USER_SESSION_KEY_PREFIX + userId);
                userSessionMap.remove(channelId);
                
                // 如果用户没有其他会话，从在线用户集合中移除
                if (userSessionMap.isEmpty()) {
                    RSet<String> onlineUsers = redissonClient.getSet(ONLINE_USERS_KEY);
                    onlineUsers.remove(userId);
                }
                
                // 删除通道信息
                redissonClient.getMap(CHANNEL_INFO_KEY_PREFIX + channelId).delete();
                
                log.info("User {} unbound from channel {} in distributed session", userId, channelId);
                
            } catch (Exception e) {
                log.error("Failed to unbind user session in Redis", e);
            }
        }
    }
    
    @Override
    public List<Channel> getUserChannels(String userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        
        // 只返回本节点的通道
        return localChannelUserMap.entrySet().stream()
            .filter(entry -> userId.equals(entry.getValue()))
            .map(Map.Entry::getKey)
            .filter(Channel::isActive)
            .collect(Collectors.toList());
    }
    
    @Override
    public String getUserId(Channel channel) {
        if (channel == null) {
            return null;
        }
        
        // 优先从通道属性获取
        String userId = channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.USER_ID_KEY).get();
        if (userId != null) {
            return userId;
        }
        
        // 从本地映射获取
        return localChannelUserMap.get(channel);
    }
    
    @Override
    public String getDeviceId(Channel channel) {
        if (channel == null) {
            return null;
        }
        
        return channel.attr(io.github.chenygs.im.core.session.LocalSessionManager.DEVICE_ID_KEY).get();
    }
    
    @Override
    public Set<String> getOnlineUserIds() {
        if (redissonClient != null) {
            try {
                RSet<String> onlineUsers = redissonClient.getSet(ONLINE_USERS_KEY);
                return new HashSet<>(onlineUsers.readAll());
            } catch (Exception e) {
                log.error("Failed to get online users from Redis", e);
            }
        }
        
        // 降级到本地会话
        return new HashSet<>(localChannelUserMap.values());
    }
    
    @Override
    public boolean isUserOnline(String userId) {
        if (userId == null) {
            return false;
        }
        
        if (redissonClient != null) {
            try {
                RSet<String> onlineUsers = redissonClient.getSet(ONLINE_USERS_KEY);
                return onlineUsers.contains(userId);
            } catch (Exception e) {
                log.error("Failed to check user online status in Redis", e);
            }
        }
        
        // 降级到本地检查
        return localChannelUserMap.containsValue(userId);
    }
    
    @Override
    public long getOnlineUserCount() {
        if (redissonClient != null) {
            try {
                RSet<String> onlineUsers = redissonClient.getSet(ONLINE_USERS_KEY);
                return onlineUsers.size();
            } catch (Exception e) {
                log.error("Failed to get online user count from Redis", e);
            }
        }
        
        // 降级到本地统计
        return new HashSet<>(localChannelUserMap.values()).size();
    }
    
    @Override
    public void cleanInactiveChannels() {
        // 清理本地无效通道
        Iterator<Map.Entry<Channel, String>> iterator = localChannelUserMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Channel, String> entry = iterator.next();
            Channel channel = entry.getKey();
            String userId = entry.getValue();
            
            if (!channel.isActive()) {
                iterator.remove();
                localChannelMap.remove(channel.id().asLongText());
                
                // 从Redis中清理
                unbindUserChannel(userId, channel);
                
                log.debug("Cleaned inactive channel: {} for user: {}", 
                    channel.id().asShortText(), userId);
            }
        }
    }
    
    /**
     * 获取用户在指定节点的会话信息
     */
    public List<SessionInfo> getUserSessionsOnNode(String userId, String nodeId) {
        if (redissonClient == null || userId == null) {
            return Collections.emptyList();
        }
        
        try {
            RMap<String, SessionInfo> userSessionMap = redissonClient.getMap(USER_SESSION_KEY_PREFIX + userId);
            return userSessionMap.readAllValues().stream()
                .filter(session -> nodeId == null || nodeId.equals(session.getNodeId()))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get user sessions from Redis", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        // 这里可以从配置或其他地方获取节点ID
        // 简单实现：使用主机名
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown-node";
        }
    }
    
    /**
     * 会话信息模型
     */
    public static class SessionInfo {
        private String channelId;
        private String userId;
        private String deviceId;
        private String nodeId;
        private Long connectTime;
        
        // Getters and Setters
        public String getChannelId() { return channelId; }
        public SessionInfo setChannelId(String channelId) { this.channelId = channelId; return this; }
        
        public String getUserId() { return userId; }
        public SessionInfo setUserId(String userId) { this.userId = userId; return this; }
        
        public String getDeviceId() { return deviceId; }
        public SessionInfo setDeviceId(String deviceId) { this.deviceId = deviceId; return this; }
        
        public String getNodeId() { return nodeId; }
        public SessionInfo setNodeId(String nodeId) { this.nodeId = nodeId; return this; }
        
        public Long getConnectTime() { return connectTime; }
        public SessionInfo setConnectTime(Long connectTime) { this.connectTime = connectTime; return this; }
    }
}
