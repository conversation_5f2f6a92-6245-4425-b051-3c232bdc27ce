package io.github.chenygs.im.core.session;

import io.github.chenygs.im.core.id.MessageIdGenerator;
import io.netty.channel.Channel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 本地会话管理器实现
 * 支持完整的ChannelContext管理和多端登录
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LocalSessionManager implements SessionManager {

    @Autowired
    private MessageIdGenerator messageIdGenerator;

    /**
     * 通道上下文属性键
     */
    public static final AttributeKey<ChannelContext> CHANNEL_CONTEXT_KEY = AttributeKey.valueOf("channelContext");

    /**
     * 会话ID -> 通道上下文映射
     */
    private final Map<String, ChannelContext> sessionContextMap = new ConcurrentHashMap<>();

    /**
     * 通道 -> 会话上下文映射
     */
    private final Map<Channel, ChannelContext> channelContextMap = new ConcurrentHashMap<>();

    /**
     * 用户ID -> 会话上下文列表映射
     * 支持同一用户多端登录
     */
    private final Map<String, List<ChannelContext>> userSessionMap = new ConcurrentHashMap<>();

    /**
     * 统计信息
     */
    private final AtomicLong totalSessionCount = new AtomicLong(0);
    private final AtomicLong totalMessageCount = new AtomicLong(0);
    private final AtomicLong totalByteCount = new AtomicLong(0);

    @Override
    public ChannelContext createChannelContext(Channel channel, String protocol) {
        if (channel == null) {
            throw new IllegalArgumentException("Channel cannot be null");
        }

        // 生成会话ID
        String sessionId = messageIdGenerator.nextId("SESSION");

        // 创建通道上下文
        ChannelContext context = new ChannelContext()
                .setSessionId(sessionId)
                .setChannel(channel)
                .setProtocol(protocol)
                .setOnlineTime(LocalDateTime.now())
                .setLastActiveTime(LocalDateTime.now())
                .setNodeId(getCurrentNodeId());

        // 设置网络信息
        setNetworkInfo(context, channel);

        // 存储映射关系
        sessionContextMap.put(sessionId, context);
        channelContextMap.put(channel, context);

        // 设置通道属性
        channel.attr(CHANNEL_CONTEXT_KEY).set(context);

        // 更新统计
        totalSessionCount.incrementAndGet();

        log.info("Created channel context: sessionId={}, protocol={}, clientIp={}",
                sessionId, protocol, context.getClientIp());

        return context;
    }

    @Override
    public void bindUserChannel(String userId, ChannelContext channelContext, String deviceId) {
        if (userId == null || channelContext == null) {
            log.warn("UserId or channelContext is null, skip binding");
            return;
        }

        // 设置用户信息
        channelContext.setUserId(userId)
                     .setDeviceId(deviceId)
                     .setAuthenticated(userId);

        // 绑定用户和会话
        userSessionMap.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(channelContext);

        log.info("User {} bound to session {}, deviceId: {}, protocol: {}",
                userId, channelContext.getSessionId(), deviceId, channelContext.getProtocol());
    }

    @Override
    public void unbindUserChannel(String userId, Channel channel) {
        if (userId == null || channel == null) {
            return;
        }

        ChannelContext context = channelContextMap.get(channel);
        if (context != null) {
            // 设置断开状态
            context.setStatus(ChannelContext.ConnectionStatus.DISCONNECTED);

            // 从用户会话映射中移除
            List<ChannelContext> userSessions = userSessionMap.get(userId);
            if (userSessions != null) {
                userSessions.remove(context);
                if (userSessions.isEmpty()) {
                    userSessionMap.remove(userId);
                }
            }

            // 从映射中移除
            sessionContextMap.remove(context.getSessionId());
            channelContextMap.remove(channel);

            // 清理通道属性
            channel.attr(CHANNEL_CONTEXT_KEY).set(null);

            log.info("User {} unbound from session {}, duration: {} minutes",
                    userId, context.getSessionId(),
                    java.time.Duration.between(context.getOnlineTime(), LocalDateTime.now()).toMinutes());
        }
    }

    @Override
    public ChannelContext getChannelContext(Channel channel) {
        return channelContextMap.get(channel);
    }

    @Override
    public ChannelContext getChannelContext(String sessionId) {
        return sessionContextMap.get(sessionId);
    }

    @Override
    public List<ChannelContext> getUserChannelContexts(String userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        List<ChannelContext> contexts = userSessionMap.get(userId);
        if (contexts == null) {
            return Collections.emptyList();
        }

        // 过滤掉无效的会话
        List<ChannelContext> activeContexts = contexts.stream()
                .filter(ChannelContext::isActive)
                .collect(Collectors.toList());

        // 如果有无效会话，更新映射
        if (activeContexts.size() != contexts.size()) {
            if (activeContexts.isEmpty()) {
                userSessionMap.remove(userId);
            } else {
                userSessionMap.put(userId, new CopyOnWriteArrayList<>(activeContexts));
            }

            // 清理无效会话
            contexts.stream()
                    .filter(context -> !context.isActive())
                    .forEach(context -> {
                        sessionContextMap.remove(context.getSessionId());
                        channelContextMap.remove(context.getChannel());
                    });
        }

        return new ArrayList<>(activeContexts);
    }

    @Override
    public List<Channel> getUserChannels(String userId) {
        return getUserChannelContexts(userId).stream()
                .map(ChannelContext::getChannel)
                .collect(Collectors.toList());
    }

    @Override
    public String getUserId(Channel channel) {
        ChannelContext context = getChannelContext(channel);
        return context != null ? context.getUserId() : null;
    }

    @Override
    public String getDeviceId(Channel channel) {
        ChannelContext context = getChannelContext(channel);
        return context != null ? context.getDeviceId() : null;
    }

    @Override
    public Set<String> getOnlineUserIds() {
        // 清理无效会话后返回在线用户ID
        cleanInactiveChannels();
        return new HashSet<>(userSessionMap.keySet());
    }

    @Override
    public boolean isUserOnline(String userId) {
        if (userId == null) {
            return false;
        }

        List<ChannelContext> contexts = getUserChannelContexts(userId);
        return !contexts.isEmpty();
    }

    @Override
    public long getOnlineUserCount() {
        cleanInactiveChannels();
        return userSessionMap.size();
    }

    @Override
    public List<ChannelContext> getAllChannelContexts() {
        return new ArrayList<>(channelContextMap.values());
    }

    @Override
    public void updateLastActiveTime(Channel channel) {
        ChannelContext context = getChannelContext(channel);
        if (context != null) {
            context.updateLastActiveTime();
        }
    }

    @Override
    public void cleanInactiveChannels() {
        Iterator<Map.Entry<String, List<ChannelContext>>> userIterator = userSessionMap.entrySet().iterator();

        while (userIterator.hasNext()) {
            Map.Entry<String, List<ChannelContext>> entry = userIterator.next();
            String userId = entry.getKey();
            List<ChannelContext> contexts = entry.getValue();

            // 过滤活跃会话
            List<ChannelContext> activeContexts = contexts.stream()
                    .filter(ChannelContext::isActive)
                    .collect(Collectors.toList());

            if (activeContexts.isEmpty()) {
                // 用户所有会话都不活跃，移除用户
                userIterator.remove();
                log.debug("Removed user {} with no active sessions", userId);
            } else if (activeContexts.size() != contexts.size()) {
                // 有部分会话不活跃，更新会话列表
                entry.setValue(new CopyOnWriteArrayList<>(activeContexts));
                log.debug("Updated user {} active sessions: {}", userId, activeContexts.size());
            }

            // 清理无效的会话映射
            contexts.stream()
                    .filter(context -> !context.isActive())
                    .forEach(context -> {
                        sessionContextMap.remove(context.getSessionId());
                        channelContextMap.remove(context.getChannel());
                        log.debug("Cleaned inactive session: {}", context.getSessionId());
                    });
        }
    }

    @Override
    public SessionStatistics getSessionStatistics() {
        SessionStatistics stats = new SessionStatistics();
        stats.setTotalSessions(totalSessionCount.get());
        stats.setActiveSessions(channelContextMap.size());
        stats.setTotalMessages(totalMessageCount.get());
        stats.setTotalBytes(totalByteCount.get());

        // 计算已认证会话数
        long authenticatedCount = channelContextMap.values().stream()
                .filter(ChannelContext::isAuthenticated)
                .count();
        stats.setAuthenticatedSessions(authenticatedCount);

        return stats;
    }

    /**
     * 设置网络信息
     */
    private void setNetworkInfo(ChannelContext context, Channel channel) {
        try {
            // 客户端地址信息
            InetSocketAddress clientAddress = (InetSocketAddress) channel.remoteAddress();
            if (clientAddress != null) {
                context.setClientIp(clientAddress.getAddress().getHostAddress())
                       .setClientPort(clientAddress.getPort());
            }

            // 服务端地址信息
            InetSocketAddress serverAddress = (InetSocketAddress) channel.localAddress();
            if (serverAddress != null) {
                context.setServerIp(serverAddress.getAddress().getHostAddress())
                       .setServerPort(serverAddress.getPort());
            }
        } catch (Exception e) {
            log.warn("Failed to set network info for channel {}", channel.id().asShortText(), e);
        }
    }

    /**
     * 获取当前节点ID
     */
    private String getCurrentNodeId() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown-node";
        }
    }

    /**
     * 根据设备ID获取用户会话上下文
     */
    public ChannelContext getUserChannelContextByDevice(String userId, String deviceId) {
        if (userId == null || deviceId == null) {
            return null;
        }

        List<ChannelContext> contexts = getUserChannelContexts(userId);
        return contexts.stream()
                .filter(context -> deviceId.equals(context.getDeviceId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据设备ID获取用户通道
     */
    public Channel getUserChannelByDevice(String userId, String deviceId) {
        ChannelContext context = getUserChannelContextByDevice(userId, deviceId);
        return context != null ? context.getChannel() : null;
    }

    /**
     * 获取所有活跃会话数量
     */
    public int getActiveSessionCount() {
        return channelContextMap.size();
    }

    /**
     * 根据协议类型获取会话数量
     */
    public long getSessionCountByProtocol(String protocol) {
        return channelContextMap.values().stream()
                .filter(context -> protocol.equals(context.getProtocol()))
                .count();
    }

    /**
     * 获取用户在指定协议下的会话
     */
    public List<ChannelContext> getUserChannelContextsByProtocol(String userId, String protocol) {
        return getUserChannelContexts(userId).stream()
                .filter(context -> protocol.equals(context.getProtocol()))
                .collect(Collectors.toList());
    }

    /**
     * 更新消息统计
     */
    public void updateMessageStatistics(Channel channel, boolean sent, int bytes) {
        ChannelContext context = getChannelContext(channel);
        if (context != null) {
            if (sent) {
                context.getStatistics().incrementSentMessage(bytes);
            } else {
                context.getStatistics().incrementReceivedMessage(bytes);
            }

            // 更新全局统计
            totalMessageCount.incrementAndGet();
            totalByteCount.addAndGet(bytes);

            // 更新活跃时间
            context.updateLastActiveTime();
        }
    }

    /**
     * 更新心跳统计
     */
    public void updateHeartbeatStatistics(Channel channel) {
        ChannelContext context = getChannelContext(channel);
        if (context != null) {
            context.getStatistics().incrementHeartbeat();
            context.updateLastActiveTime();
        }
    }

    /**
     * 更新错误统计
     */
    public void updateErrorStatistics(Channel channel) {
        ChannelContext context = getChannelContext(channel);
        if (context != null) {
            context.getStatistics().incrementError();
        }
    }
}
